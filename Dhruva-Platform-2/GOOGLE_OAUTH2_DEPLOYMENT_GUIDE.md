# Google OAuth2 Deployment Guide for Dhruva Platform

## 🚀 Quick Start Deployment

### Prerequisites Checklist
- [ ] Google Cloud Console project created
- [ ] OAuth2 Client ID and Client Secret obtained
- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] Database collections ready

---

## 📋 Step-by-Step Deployment

### Step 1: Install Required Dependencies

Add these dependencies to your `requirements.txt`:

```bash
# OAuth2 and HTTP client dependencies
httpx>=0.24.0
cryptography>=3.4.8

# Existing dependencies (keep these)
fastapi>=0.68.0
pydantic>=1.8.0
pymongo>=3.12.0
python-jose[cryptography]>=3.3.0
```

Install dependencies:
```bash
pip install -r requirements.txt
```

### Step 2: Set Up Environment Variables

1. **Copy the example environment file:**
```bash
cp .env.oauth.example .env.oauth
```

2. **Edit `.env.oauth` with your actual credentials:**
```bash
# Replace these with your actual Google OAuth2 credentials
GOOGLE_OAUTH_CLIENT_ID=your-actual-client-id.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=your-actual-client-secret
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/auth/oauth/google/callback

# Generate secure keys
OAUTH_STATE_SECRET_KEY=$(openssl rand -base64 32)
OAUTH_TOKEN_ENCRYPTION_KEY=$(openssl rand -base64 32)
```

3. **Source the environment file:**
```bash
source .env.oauth
# OR add to your existing .env file
cat .env.oauth >> .env
```

### Step 3: Update Main Application

Add OAuth router to your main application file (`main.py`):

```python
from fastapi import FastAPI
from module.auth.router.oauth_router import router as oauth_router

app = FastAPI(title="Dhruva Platform")

# Add OAuth router
app.include_router(oauth_router, prefix="/auth", tags=["Authentication"])

# Keep existing routers
# app.include_router(auth_router, prefix="/auth")
# app.include_router(services_router, prefix="/services")
# ... other routers
```

### Step 4: Database Setup

The OAuth implementation will automatically create the necessary collections:
- `oauth_state` - Temporary OAuth state storage
- Enhanced `user` collection with OAuth fields
- Enhanced `pending_registration` collection

No manual database setup required - collections will be created on first use.

### Step 5: Test the Implementation

1. **Start the server:**
```bash
python main.py
# OR
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

2. **Test OAuth login initiation:**
```bash
curl -X GET "http://localhost:8000/auth/oauth/google/login"
```

3. **Check the redirect URL:**
The response should be a 302 redirect to Google's OAuth authorization URL.

### Step 6: Frontend Integration

Update your frontend to include OAuth login buttons:

```html
<!-- Add to your login page -->
<a href="/auth/oauth/google/login" class="oauth-login-btn">
  <img src="google-logo.png" alt="Google" />
  Sign in with Google
</a>
```

Handle OAuth success/error redirects:
```javascript
// Handle OAuth callback on success page
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');
const error = urlParams.get('error');

if (token) {
  // Store JWT token and redirect to dashboard
  localStorage.setItem('authToken', token);
  window.location.href = '/dashboard';
} else if (error) {
  // Handle OAuth error
  console.error('OAuth error:', error);
  alert('Authentication failed. Please try again.');
}
```

---

## 🐳 Docker Deployment

### Update Docker Configuration

1. **Add environment variables to docker-compose:**

```yaml
# docker-compose-app.yml
services:
  dhruva-platform-server:
    environment:
      # Existing environment variables
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - MONGO_APP_DB_USERNAME=${MONGO_APP_DB_USERNAME}
      # ... other existing vars
      
      # New OAuth environment variables
      - GOOGLE_OAUTH_CLIENT_ID=${GOOGLE_OAUTH_CLIENT_ID}
      - GOOGLE_OAUTH_CLIENT_SECRET=${GOOGLE_OAUTH_CLIENT_SECRET}
      - GOOGLE_OAUTH_REDIRECT_URI=${GOOGLE_OAUTH_REDIRECT_URI}
      - OAUTH_STATE_SECRET_KEY=${OAUTH_STATE_SECRET_KEY}
      - OAUTH_TOKEN_ENCRYPTION_KEY=${OAUTH_TOKEN_ENCRYPTION_KEY}
      - FRONTEND_BASE_URL=${FRONTEND_BASE_URL}
      - OAUTH_SUCCESS_REDIRECT_URL=${OAUTH_SUCCESS_REDIRECT_URL}
      - OAUTH_ERROR_REDIRECT_URL=${OAUTH_ERROR_REDIRECT_URL}
```

2. **Rebuild and restart containers:**
```bash
# Rebuild the server image
docker build -t dhruva-platform-server:latest-pg15 .

# Restart all services
docker compose -f docker-compose-db.yml -f docker-compose-metering.yml -f docker-compose-monitoring.yml -f docker-compose-app.yml up --force-recreate -d --remove-orphans
```

---

## 🔧 Production Configuration

### Production Environment Variables

```bash
# Production OAuth2 Configuration
GOOGLE_OAUTH_CLIENT_ID=your-production-client-id.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=your-production-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://api.your-domain.com/auth/oauth/google/callback

# Production Security Settings
OAUTH_REQUIRE_HTTPS=true
OAUTH_SECURE_COOKIES=true
OAUTH_SAME_SITE_COOKIES=strict

# Production Frontend URLs
FRONTEND_BASE_URL=https://your-domain.com
OAUTH_SUCCESS_REDIRECT_URL=https://your-domain.com/oauth/success
OAUTH_ERROR_REDIRECT_URL=https://your-domain.com/oauth/error

# Secure Keys (generate new ones for production)
OAUTH_STATE_SECRET_KEY=$(openssl rand -base64 32)
OAUTH_TOKEN_ENCRYPTION_KEY=$(openssl rand -base64 32)
```

### HTTPS Configuration

Ensure your production deployment uses HTTPS:

1. **Update redirect URIs in Google Cloud Console:**
   - Change from `http://localhost:8000/auth/oauth/google/callback`
   - To `https://api.your-domain.com/auth/oauth/google/callback`

2. **Configure SSL certificates:**
   - Use Let's Encrypt, AWS Certificate Manager, or your preferred SSL provider
   - Ensure certificates are properly configured in your reverse proxy (Nginx)

3. **Update CORS settings:**
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://your-domain.com"],  # Production domain
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

---

## 🧪 Testing and Validation

### Manual Testing Checklist

1. **OAuth Flow Testing:**
   - [ ] OAuth login initiation works
   - [ ] Redirects to Google correctly
   - [ ] Callback handles authorization code
   - [ ] User creation/update works
   - [ ] JWT token generation works
   - [ ] Success redirect works

2. **Security Testing:**
   - [ ] PKCE parameters generated correctly
   - [ ] State parameter prevents CSRF
   - [ ] Tokens are encrypted at rest
   - [ ] Invalid state rejected
   - [ ] Expired state cleaned up

3. **Integration Testing:**
   - [ ] Existing JWT auth still works
   - [ ] Existing API key auth still works
   - [ ] OAuth users can use API keys
   - [ ] Email verification works for unverified OAuth emails

### Automated Testing

Run the test suite:
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run OAuth tests
python -m pytest tests/test_google_oauth_integration.py -v

# Run all tests
python -m pytest tests/ -v
```

### Load Testing

Test OAuth endpoints under load:
```bash
# Install load testing tool
pip install locust

# Create load test script
cat > oauth_load_test.py << EOF
from locust import HttpUser, task, between

class OAuthUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def test_oauth_login(self):
        self.client.get("/auth/oauth/google/login")
EOF

# Run load test
locust -f oauth_load_test.py --host=http://localhost:8000
```

---

## 📊 Monitoring and Maintenance

### Logging Configuration

Add OAuth-specific logging:
```python
import logging

# Configure OAuth logging
oauth_logger = logging.getLogger("oauth")
oauth_logger.setLevel(logging.INFO)

# Log OAuth events
oauth_logger.info(f"OAuth login initiated for provider: {provider}")
oauth_logger.info(f"OAuth user created: {user.email}")
oauth_logger.warning(f"OAuth token refresh failed for user: {user_id}")
```

### Metrics to Monitor

1. **OAuth Success Rate:** Percentage of successful OAuth flows
2. **Provider Response Time:** Time taken for Google API calls
3. **Token Refresh Rate:** Frequency of token refreshes
4. **Error Rates:** OAuth-specific error rates
5. **User Adoption:** Percentage of users using OAuth vs traditional auth

### Maintenance Tasks

1. **Regular OAuth State Cleanup:**
```python
# Add to your scheduled tasks
from module.auth.repository.oauth_state_repository import OAuthStateRepository

async def cleanup_expired_oauth_states():
    repo = OAuthStateRepository()
    deleted_count = repo.cleanup_expired_states()
    print(f"Cleaned up {deleted_count} expired OAuth states")
```

2. **Token Health Monitoring:**
```python
# Monitor OAuth token health
async def check_oauth_token_health():
    # Check for tokens expiring soon
    # Attempt token refresh for critical users
    # Alert on high failure rates
    pass
```

---

## 🚨 Troubleshooting

### Common Issues and Solutions

1. **"Invalid redirect URI" Error:**
   - Verify redirect URI in Google Cloud Console matches exactly
   - Check for HTTP vs HTTPS mismatch
   - Ensure no trailing slashes

2. **"Invalid client" Error:**
   - Verify Client ID and Client Secret are correct
   - Check environment variables are loaded
   - Ensure credentials are for the correct project

3. **Token Encryption Errors:**
   - Verify `OAUTH_TOKEN_ENCRYPTION_KEY` is set
   - Ensure key is at least 32 characters
   - Check cryptography library is installed

4. **Database Connection Issues:**
   - Verify MongoDB connection string
   - Check database permissions
   - Ensure collections can be created

### Debug Mode

Enable debug logging:
```bash
export OAUTH_DEBUG_MODE=true
export PYTHONPATH="${PYTHONPATH}:$(pwd)/server"
python main.py
```

---

## ✅ Deployment Verification

After deployment, verify everything works:

1. **Health Check:**
```bash
curl -f http://localhost:8000/health
```

2. **OAuth Endpoint Check:**
```bash
curl -X GET "http://localhost:8000/auth/oauth/google/login"
```

3. **Database Check:**
```bash
# Check if OAuth collections exist
docker exec -it dhruva-platform-app-db mongosh --username dhruvaadmin --password dhruva123 --authenticationDatabase admin --eval "use admin; db.oauth_state.countDocuments()"
```

4. **Log Check:**
```bash
# Check server logs for OAuth-related messages
docker logs dhruva-platform-server --tail 100 | grep -i oauth
```

---

## 🎉 Success!

If all checks pass, your Google OAuth2 integration is successfully deployed! Users can now:

- Sign in with their Google accounts
- Link Google accounts to existing accounts
- Use OAuth authentication alongside existing methods
- Enjoy enhanced security with PKCE and token encryption

Next steps: Consider adding GitHub and Microsoft OAuth2 support following the same patterns.
