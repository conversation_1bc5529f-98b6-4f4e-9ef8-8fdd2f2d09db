# Google OAuth2 Configuration
# Replace these placeholder values with your actual Google OAuth2 credentials
# Follow the setup guide in GOOGLE_OAUTH2_CREDENTIALS_SETUP.md

# Google OAuth2 Credentials (ACTUAL CREDENTIALS)
GOOGLE_OAUTH_CLIENT_ID=************-lhf7ekmbta2hjp0dd580lvm0301o1lue.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=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
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/auth/oauth/google/callback

# OAuth2 Security (REQUIRED)
OAUTH_STATE_SECRET_KEY=your-oauth-state-secret-key-here-32-chars-min
OAUTH_TOKEN_ENCRYPTION_KEY=your-32-byte-encryption-key-for-oauth-tokens

# Frontend URLs for <PERSON><PERSON><PERSON> Redirects
FRONTEND_BASE_URL=http://localhost:3000
OAUTH_SUCCESS_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/success
OAUTH_ERROR_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/error

# OAuth2 Settings (Optional)
OAUTH_STATE_EXPIRY_MINUTES=10
OAUTH_TOKEN_REFRESH_THRESHOLD_MINUTES=30
OAUTH_REQUIRE_HTTPS=false
OAUTH_SECURE_COOKIES=false
OAUTH_SAME_SITE_COOKIES=lax

# Existing Configuration (Keep these from your current .env)
JWT_SECRET_KEY=your-existing-jwt-secret-key

# Database Configuration (Keep existing)
MONGO_APP_DB_USERNAME=dhruvaadmin
MONGO_APP_DB_PASSWORD=dhruva123
APP_DB_NAME=admin
APP_DB_CONNECTION_STRING=mongodb://${MONGO_APP_DB_USERNAME}:${MONGO_APP_DB_PASSWORD}@dhruva-platform-app-db:27017/admin?authSource=admin

# Redis Configuration (Keep existing)
REDIS_HOST=dhruva-platform-redis
REDIS_PORT=6379
REDIS_PASSWORD=dhruva123
REDIS_DB=0
REDIS_SECURE=false

# Email Configuration (Keep existing)
EMAIL_SERVICE_TYPE=SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Production OAuth2 Configuration (for production deployment)
# GOOGLE_OAUTH_CLIENT_ID=your-production-client-id.apps.googleusercontent.com
# GOOGLE_OAUTH_CLIENT_SECRET=your-production-client-secret
# GOOGLE_OAUTH_REDIRECT_URI=https://api.your-domain.com/auth/oauth/google/callback
# FRONTEND_BASE_URL=https://your-domain.com
# OAUTH_REQUIRE_HTTPS=true
# OAUTH_SECURE_COOKIES=true
# OAUTH_SAME_SITE_COOKIES=strict
