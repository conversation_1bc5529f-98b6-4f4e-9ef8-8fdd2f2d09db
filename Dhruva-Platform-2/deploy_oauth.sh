#!/bin/bash

# Dhruva Platform OAuth2 Docker Deployment Script
echo "🐳 Deploying Dhruva Platform with Google OAuth2 support..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "server/main.py" ]; then
    print_error "Please run this script from the Dhruva-Platform-2 root directory"
    exit 1
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if docker-compose files exist
if [ ! -f "docker-compose-app.yml" ]; then
    print_error "docker-compose-app.yml not found"
    exit 1
fi

print_info "Starting OAuth2 deployment process..."

# 1. Stop existing containers
print_info "Stopping existing containers..."
docker compose -f docker-compose-db.yml -f docker-compose-metering.yml -f docker-compose-monitoring.yml -f docker-compose-app.yml down
print_status "Containers stopped"

# 2. Rebuild server image with OAuth dependencies
print_info "Rebuilding server image with OAuth dependencies..."
cd server
docker build -t dhruva-platform-server:latest-pg15 .
if [ $? -eq 0 ]; then
    print_status "Server image rebuilt successfully"
else
    print_error "Failed to rebuild server image"
    exit 1
fi
cd ..

# 3. Start all services with force recreate
print_info "Starting all services with OAuth support..."
docker compose -f docker-compose-db.yml -f docker-compose-metering.yml -f docker-compose-monitoring.yml -f docker-compose-app.yml up --force-recreate -d --remove-orphans

if [ $? -eq 0 ]; then
    print_status "All services started successfully"
else
    print_error "Failed to start services"
    exit 1
fi

# 4. Wait for services to be ready
print_info "Waiting for services to be ready..."
sleep 10

# 5. Check service health
print_info "Checking service health..."

# Check server health
if curl -f http://localhost:8000/health &> /dev/null; then
    print_status "Backend server is healthy"
else
    print_warning "Backend server health check failed"
fi

# Check OAuth endpoint
if curl -f http://localhost:8000/auth/oauth/google/login &> /dev/null; then
    print_status "OAuth endpoints are accessible"
else
    print_warning "OAuth endpoints may not be ready yet"
fi

# 6. Display deployment summary
echo ""
echo "🎉 OAuth2 Deployment Complete!"
echo ""
echo "📋 Deployment Summary:"
echo "✅ Docker image rebuilt with OAuth dependencies"
echo "✅ All services restarted with force recreate"
echo "✅ OAuth endpoints deployed"
echo ""
echo "🔧 Service URLs:"
echo "Backend API: http://localhost:8000"
echo "OAuth Login: http://localhost:8000/auth/oauth/google/login"
echo "Frontend: http://localhost:3001/dhruva"
echo "Grafana: http://localhost:3000"
echo "Prometheus: http://localhost:9090"
echo ""
echo "🧪 Testing OAuth:"
echo "1. Start the frontend:"
echo "   cd client && npm run dev"
echo ""
echo "2. Open browser and go to:"
echo "   http://localhost:3001/dhruva"
echo ""
echo "3. Click 'Continue with Google' button"
echo ""
echo "🔍 Troubleshooting:"
echo "- Check server logs: docker logs dhruva-platform-server --tail 100"
echo "- Check all container status: docker ps"
echo "- Verify OAuth environment variables: docker exec dhruva-platform-server env | grep OAUTH"
echo ""
echo "📚 Documentation:"
echo "- OAuth Implementation: OAUTH2_IMPLEMENTATION_SUMMARY.md"
echo "- Deployment Guide: GOOGLE_OAUTH2_DEPLOYMENT_GUIDE.md"
echo ""
print_status "Ready to test Google OAuth2 integration!"

# 7. Optional: Show container status
echo ""
print_info "Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep dhruva
