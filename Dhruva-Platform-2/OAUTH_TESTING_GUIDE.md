# OAuth2 Testing Guide for Dhruva Platform

## 🚀 Quick Start Testing

### Prerequisites
✅ Google OAuth2 credentials configured  
✅ Docker containers running  
✅ Frontend application started  

---

## 📋 Step-by-Step Testing Process

### Step 1: Deploy with OAuth Support

Run the deployment script:
```bash
./deploy_oauth.sh
```

This will:
- Rebuild Docker image with OAuth dependencies (`httpx`, `cryptography`)
- Restart all services with force recreate
- Verify OAuth endpoints are accessible

### Step 2: Start Frontend

```bash
cd client
npm run dev
```

The frontend will be available at: `http://localhost:3001`

### Step 3: Test OAuth Flow

1. **Open the login page:**
   ```
   http://localhost:3001/dhruva
   ```

2. **Look for the Google OAuth button:**
   - You should see "Continue with Google" button below the regular login form
   - The button has the Google logo and is styled with an outline

3. **Click "Continue with Google":**
   - This will redirect you to: `http://localhost:8000/auth/oauth/google/login`
   - Which then redirects to Google's OAuth consent screen

4. **Complete Google OAuth:**
   - Sign in with your Google account
   - Grant permissions for email and profile access
   - Google will redirect back to: `http://localhost:8000/auth/oauth/google/callback`

5. **Verify successful authentication:**
   - You should be redirected to: `http://localhost:3001/oauth/success`
   - After 2 seconds, automatically redirected to the services page
   - Check that you're logged in and can access the platform

---

## 🔍 Detailed Testing Scenarios

### Scenario 1: New User OAuth Registration

**Test Steps:**
1. Use a Google account that hasn't been used with Dhruva Platform before
2. Click "Continue with Google"
3. Complete OAuth flow
4. Verify new user is created with:
   - OAuth provider information
   - Default API key generated
   - CONSUMER role assigned
   - Email verified status from Google

**Expected Results:**
- User successfully logged in
- Can access services page
- API key works for inference requests

### Scenario 2: Existing User OAuth Linking

**Test Steps:**
1. First, create a traditional account (email/password)
2. Log out
3. Use the same email address for Google OAuth
4. Complete OAuth flow
5. Verify account linking

**Expected Results:**
- OAuth provider added to existing user
- Can log in with either method
- All existing data preserved

### Scenario 3: OAuth Error Handling

**Test Steps:**
1. Start OAuth flow but deny permissions
2. Verify error page shows appropriate message
3. Try OAuth with invalid state (manually modify URL)
4. Verify security error handling

**Expected Results:**
- Proper error messages displayed
- Security violations blocked
- User can retry authentication

---

## 🧪 Backend API Testing

### Test OAuth Endpoints Directly

1. **Test OAuth Login Initiation:**
   ```bash
   curl -X GET "http://localhost:8000/auth/oauth/google/login"
   ```
   **Expected:** 302 redirect to Google OAuth URL

2. **Test OAuth Status (requires authentication):**
   ```bash
   curl -X GET "http://localhost:8000/auth/oauth/google/status" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "x-auth-source: OAUTH_TOKEN"
   ```

3. **Test API with OAuth Token:**
   ```bash
   curl -X GET "http://localhost:8000/services/details/list_models" \
        -H "Authorization: Bearer YOUR_OAUTH_JWT_TOKEN" \
        -H "x-auth-source: OAUTH_TOKEN"
   ```

### Verify Database Changes

1. **Check OAuth user creation:**
   ```bash
   docker exec -it dhruva-platform-app-db mongosh --username dhruvaadmin --password dhruva123 --authenticationDatabase admin --eval "
   use admin; 
   db.user.find({oauth_providers: {\$exists: true, \$ne: []}}).pretty()
   "
   ```

2. **Check OAuth state cleanup:**
   ```bash
   docker exec -it dhruva-platform-app-db mongosh --username dhruvaadmin --password dhruva123 --authenticationDatabase admin --eval "
   use admin; 
   db.oauth_state.countDocuments()
   "
   ```

---

## 🔧 Troubleshooting Common Issues

### Issue 1: "Continue with Google" Button Not Visible

**Symptoms:** OAuth button doesn't appear on login page  
**Solutions:**
1. Check frontend console for JavaScript errors
2. Verify `initiateGoogleOAuth` function is imported
3. Check if button is hidden by CSS

### Issue 2: OAuth Redirect Fails

**Symptoms:** Error after clicking Google OAuth button  
**Solutions:**
1. Verify Google Cloud Console redirect URIs:
   - Should include: `http://localhost:8000/auth/oauth/google/callback`
2. Check server logs: `docker logs dhruva-platform-server --tail 100`
3. Verify environment variables are loaded

### Issue 3: "Invalid Client" Error

**Symptoms:** Google shows invalid client error  
**Solutions:**
1. Verify Client ID and Client Secret in `.env` file
2. Check Google Cloud Console project is active
3. Ensure OAuth consent screen is configured

### Issue 4: Token Encryption Errors

**Symptoms:** Server errors related to token encryption  
**Solutions:**
1. Verify `OAUTH_TOKEN_ENCRYPTION_KEY` is set
2. Check cryptography library is installed in Docker image
3. Rebuild Docker image: `docker build -t dhruva-platform-server:latest-pg15 .`

### Issue 5: OAuth User Can't Access Services

**Symptoms:** OAuth user logged in but can't use services  
**Solutions:**
1. Check if default API key was created
2. Verify `x-auth-source: OAUTH_TOKEN` header is sent
3. Check OAuth token validation in backend

---

## 📊 Monitoring and Logs

### Key Log Files to Monitor

1. **Server Logs:**
   ```bash
   docker logs dhruva-platform-server --tail 100 -f
   ```

2. **Frontend Console:**
   - Open browser developer tools
   - Check Console tab for JavaScript errors
   - Check Network tab for API call failures

3. **Database Logs:**
   ```bash
   docker logs dhruva-platform-app-db --tail 50
   ```

### Success Indicators

✅ **OAuth Flow Success:**
- Server logs show successful token exchange
- User redirected to success page
- JWT token stored in localStorage
- User can access protected routes

✅ **Database Integration:**
- New user created with OAuth provider info
- Default API key generated
- OAuth state cleaned up after use

✅ **API Integration:**
- OAuth tokens work with existing endpoints
- Proper `x-auth-source` header handling
- Backward compatibility maintained

---

## 🎯 Performance Testing

### Load Testing OAuth Endpoints

1. **Install load testing tool:**
   ```bash
   pip install locust
   ```

2. **Create load test script:**
   ```python
   # oauth_load_test.py
   from locust import HttpUser, task, between
   
   class OAuthUser(HttpUser):
       wait_time = between(1, 3)
       
       @task
       def test_oauth_login(self):
           self.client.get("/auth/oauth/google/login")
   ```

3. **Run load test:**
   ```bash
   locust -f oauth_load_test.py --host=http://localhost:8000
   ```

### Performance Metrics to Monitor

- OAuth login initiation response time
- Token exchange duration
- Database query performance for OAuth users
- Memory usage with OAuth token encryption

---

## ✅ Testing Checklist

### Pre-Deployment Testing
- [ ] Docker image builds successfully
- [ ] All containers start without errors
- [ ] OAuth endpoints respond correctly
- [ ] Environment variables loaded properly

### Functional Testing
- [ ] OAuth login button appears on frontend
- [ ] Google OAuth flow completes successfully
- [ ] New users created with proper OAuth data
- [ ] Existing users can link OAuth accounts
- [ ] OAuth users can access all services
- [ ] API keys work for OAuth users

### Security Testing
- [ ] PKCE parameters generated correctly
- [ ] State parameter prevents CSRF attacks
- [ ] OAuth tokens encrypted in database
- [ ] Invalid states rejected properly
- [ ] Token expiration handled correctly

### Integration Testing
- [ ] Backward compatibility with existing auth
- [ ] OAuth works alongside traditional login
- [ ] Email verification integration works
- [ ] Role-based access control maintained

### Error Handling Testing
- [ ] OAuth errors display user-friendly messages
- [ ] Network failures handled gracefully
- [ ] Invalid tokens rejected properly
- [ ] Database errors don't expose sensitive data

---

## 🎉 Success Criteria

Your OAuth2 integration is successful when:

1. ✅ Users can log in with Google OAuth
2. ✅ New OAuth users are created automatically
3. ✅ OAuth users get default API keys
4. ✅ All existing functionality still works
5. ✅ Security measures are properly implemented
6. ✅ Error handling provides good user experience

**Ready to test? Run `./deploy_oauth.sh` and start testing!**
