# Google OAuth2 Implementation Guide for Dhruva Platform

## Table of Contents
1. [Google OAuth2 Setup Steps](#google-oauth2-setup-steps)
2. [Dhruva Platform Integration](#dhruva-platform-integration)
3. [Email Verification Strategy](#email-verification-strategy)
4. [Environment Configuration](#environment-configuration)
5. [Testing and Validation](#testing-and-validation)
6. [Security Implementation](#security-implementation)
7. [Troubleshooting](#troubleshooting)

---

## 1. Google OAuth2 Setup Steps

### 1.1 Google Cloud Console Project Setup

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project details:
   - **Project name**: `dhruva-platform-oauth`
   - **Organization**: Your organization (if applicable)
   - **Location**: Your preferred location
4. Click "Create"

#### Step 2: Enable Google+ API
1. In the Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for "Google+ API" and click on it
3. Click "Enable"
4. Also enable "Google Identity" API for OpenID Connect

#### Step 3: Configure OAuth Consent Screen
1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" user type (unless you have Google Workspace)
3. Fill in the required information:
   ```
   App name: Dhruva Platform
   User support email: <EMAIL>
   Developer contact information: <EMAIL>
   ```
4. Add scopes:
   - `openid`
   - `email`
   - `profile`
5. Add test users (for development):
   - Add your development email addresses
6. Click "Save and Continue"

### 1.2 OAuth2 Credentials Configuration

#### Step 1: Create OAuth2 Client ID
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. Choose "Web application"
4. Configure the client:
   ```
   Name: Dhruva Platform Web Client
   
   Authorized JavaScript origins:
   - http://localhost:3000 (development)
   - https://your-domain.com (production)
   
   Authorized redirect URIs:
   - http://localhost:8000/auth/oauth/google/callback (development)
   - https://api.your-domain.com/auth/oauth/google/callback (production)
   ```
5. Click "Create"
6. **Important**: Save the Client ID and Client Secret securely

#### Step 2: Download Credentials
1. Click the download button next to your OAuth2 client
2. Save the JSON file as `google-oauth-credentials.json`
3. **Never commit this file to version control**

### 1.3 Required OAuth2 Scopes

For Dhruva Platform, we need these specific scopes:

```python
GOOGLE_OAUTH_SCOPES = [
    "openid",           # OpenID Connect for authentication
    "email",            # User's email address
    "profile",          # User's basic profile information
]
```

**Scope Descriptions:**
- `openid`: Enables OpenID Connect authentication
- `email`: Access to user's email address and verification status
- `profile`: Access to user's name, profile picture, and basic info

---

## 2. Dhruva Platform Integration

### 2.1 Enhanced User Model

#### Update User Model
Create/update `server/module/auth/model/user.py`:

```python
from datetime import datetime
from typing import List, Optional
from db.MongoBaseModel import MongoBaseModel
from pydantic import BaseModel, EmailStr
from schema.auth.common import RoleType

class OAuthProvider(BaseModel):
    """OAuth provider information for a user."""
    provider: str  # "google", "github", "microsoft"
    provider_user_id: str
    email: EmailStr
    verified_email: bool
    access_token: str  # Encrypted
    refresh_token: Optional[str] = None  # Encrypted
    token_expires_at: Optional[datetime] = None
    scope: str  # OAuth scopes granted
    created_at: datetime
    last_login: datetime

class User(MongoBaseModel):
    """Enhanced User model with OAuth support."""
    name: str
    email: EmailStr
    password: Optional[str] = None  # Optional for OAuth-only users
    role: RoleType
    oauth_providers: List[OAuthProvider] = []
    created_via: str = "email"  # "email", "google", "github", "microsoft"
    email_verified: bool = False
    created_at: datetime = datetime.utcnow()
    last_login: Optional[datetime] = None
    
    def get_oauth_provider(self, provider: str) -> Optional[OAuthProvider]:
        """Get OAuth provider info by provider name."""
        for oauth_provider in self.oauth_providers:
            if oauth_provider.provider == provider:
                return oauth_provider
        return None
    
    def has_oauth_provider(self, provider: str) -> bool:
        """Check if user has linked OAuth provider."""
        return self.get_oauth_provider(provider) is not None
    
    def is_oauth_only(self) -> bool:
        """Check if user only uses OAuth authentication."""
        return self.password is None and len(self.oauth_providers) > 0
```

### 2.2 Enhanced Token Type

#### Update Token Type Enum
Update `server/auth/token_type.py`:

```python
from enum import Enum

class TokenType(Enum):
    AUTH_TOKEN = "AUTH_TOKEN"    # Existing JWT tokens
    API_KEY = "API_KEY"          # Existing API keys
    OAUTH_TOKEN = "OAUTH_TOKEN"  # New OAuth tokens
```

### 2.3 OAuth State Management

#### Create OAuth State Model
Create `server/module/auth/model/oauth_state.py`:

```python
from datetime import datetime, timedelta
from db.MongoBaseModel import MongoBaseModel
from typing import Optional

class OAuthState(MongoBaseModel):
    """Temporary storage for OAuth state and PKCE parameters."""
    state: str  # CSRF protection
    code_verifier: str  # PKCE code verifier
    code_challenge: str  # PKCE code challenge
    provider: str  # OAuth provider name
    redirect_uri: str  # Original redirect URI
    user_id: Optional[str] = None  # For account linking
    created_at: datetime = datetime.utcnow()
    expires_at: datetime = datetime.utcnow() + timedelta(minutes=10)
    
    class Config:
        # TTL index for automatic cleanup
        indexes = [
            {
                "key": [("expires_at", 1)],
                "expireAfterSeconds": 0
            }
        ]
```

### 2.4 Google OAuth2 Service

#### Create Google OAuth Service
Create `server/module/auth/service/google_oauth_service.py`:

```python
import os
import secrets
import base64
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import httpx
from fastapi import HTTPException, status
from pydantic import BaseModel

class GoogleOAuthConfig:
    """Google OAuth2 configuration."""
    CLIENT_ID = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
    CLIENT_SECRET = os.environ.get("GOOGLE_OAUTH_CLIENT_SECRET")
    REDIRECT_URI = os.environ.get("GOOGLE_OAUTH_REDIRECT_URI")
    
    # Google OAuth2 endpoints
    AUTHORIZATION_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    TOKEN_URL = "https://oauth2.googleapis.com/token"
    USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"
    
    # Required scopes
    SCOPES = ["openid", "email", "profile"]

class GoogleUserInfo(BaseModel):
    """Google user information from OAuth."""
    id: str
    email: str
    verified_email: bool
    name: str
    given_name: str
    family_name: str
    picture: str

class GoogleOAuthTokens(BaseModel):
    """Google OAuth tokens."""
    access_token: str
    refresh_token: Optional[str] = None
    expires_in: int
    token_type: str
    scope: str

class GoogleOAuthService:
    """Google OAuth2 service implementation."""
    
    def __init__(self):
        self.config = GoogleOAuthConfig()
        if not all([self.config.CLIENT_ID, self.config.CLIENT_SECRET]):
            raise ValueError("Google OAuth credentials not configured")
    
    def generate_pkce_pair(self) -> Tuple[str, str]:
        """Generate PKCE code verifier and challenge."""
        # Generate code verifier (43-128 characters)
        code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
        
        # Generate code challenge
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return code_verifier, code_challenge
    
    def generate_authorization_url(
        self, 
        state: str, 
        code_challenge: str,
        redirect_uri: Optional[str] = None
    ) -> str:
        """Generate Google OAuth authorization URL."""
        redirect_uri = redirect_uri or self.config.REDIRECT_URI
        
        params = {
            "client_id": self.config.CLIENT_ID,
            "redirect_uri": redirect_uri,
            "scope": " ".join(self.config.SCOPES),
            "response_type": "code",
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "access_type": "offline",  # Request refresh token
            "prompt": "consent"  # Force consent screen for refresh token
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{self.config.AUTHORIZATION_URL}?{query_string}"
    
    async def exchange_code_for_tokens(
        self, 
        code: str, 
        code_verifier: str,
        redirect_uri: Optional[str] = None
    ) -> GoogleOAuthTokens:
        """Exchange authorization code for access tokens."""
        redirect_uri = redirect_uri or self.config.REDIRECT_URI
        
        data = {
            "client_id": self.config.CLIENT_ID,
            "client_secret": self.config.CLIENT_SECRET,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
            "code_verifier": code_verifier
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.config.TOKEN_URL,
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to exchange code for tokens: {response.text}"
                )
            
            token_data = response.json()
            return GoogleOAuthTokens(**token_data)
    
    async def get_user_info(self, access_token: str) -> GoogleUserInfo:
        """Get user information from Google."""
        headers = {"Authorization": f"Bearer {access_token}"}
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                self.config.USERINFO_URL,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to get user info: {response.text}"
                )
            
            user_data = response.json()
            return GoogleUserInfo(**user_data)
    
    async def refresh_access_token(self, refresh_token: str) -> GoogleOAuthTokens:
        """Refresh Google OAuth access token."""
        data = {
            "client_id": self.config.CLIENT_ID,
            "client_secret": self.config.CLIENT_SECRET,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.config.TOKEN_URL,
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to refresh token: {response.text}"
                )
            
            token_data = response.json()
            return GoogleOAuthTokens(**token_data)
```

### 2.5 OAuth Authentication Provider

#### Create OAuth Authentication Provider
Create `server/auth/oauth_provider.py`:

```python
import os
import jwt
from datetime import datetime
from typing import Optional
from fastapi import Request
from pymongo.database import Database
from bson.objectid import ObjectId
from module.auth.model.user import User
from module.auth.service.google_oauth_service import GoogleOAuthService

class OAuthAuthenticationProvider:
    """OAuth authentication provider for validating OAuth tokens."""
    
    def __init__(self):
        self.google_service = GoogleOAuthService()
    
    def validate_credentials(self, credentials: str, request: Request, db: Database) -> bool:
        """Validate OAuth credentials (JWT token with OAuth context)."""
        try:
            # Decode JWT token (should contain OAuth user info)
            claims = jwt.decode(
                credentials, 
                key=os.environ["JWT_SECRET_KEY"], 
                algorithms=["HS256"]
            )
            
            # Verify this is an OAuth token
            if claims.get("auth_type") != "oauth":
                return False
            
            # Get user from database
            user_collection = db["user"]
            user = user_collection.find_one({"_id": ObjectId(claims["sub"])})
            
            if not user:
                return False
            
            # Populate request state
            request.state.user_id = claims["sub"]
            request.state.auth_type = "oauth"
            request.state.oauth_provider = claims.get("oauth_provider")
            
            # For inference/feedback endpoints, get default API key
            if "inference" in request.url.path or "feedback" in request.url.path:
                api_key_collection = db["api_key"]
                api_key = api_key_collection.find_one(
                    {"name": "default", "user_id": ObjectId(claims["sub"])}
                )
                
                if api_key:
                    request.state.api_key_id = api_key["_id"]
                    request.state.api_key_type = api_key["type"]
                    request.state.api_key_name = "default"
            
            return True
            
        except Exception:
            return False
    
    def fetch_session(self, credentials: str, db: Database) -> dict:
        """Fetch session information for OAuth user."""
        try:
            claims = jwt.decode(
                credentials, 
                key=os.environ["JWT_SECRET_KEY"], 
                algorithms=["HS256"]
            )
            
            user_collection = db["user"]
            user = user_collection.find_one({"_id": ObjectId(claims["sub"])})
            
            if not user:
                raise Exception("User not found")
            
            return {
                "_id": user["_id"],
                "name": user["name"],
                "email": user["email"],
                "role": user["role"]
            }
            
        except Exception:
            raise Exception("Invalid OAuth session")

# Global instance
oauth_provider = OAuthAuthenticationProvider()
```

### 2.6 Enhanced AuthProvider

#### Update AuthProvider
Update `server/auth/auth_provider.py`:

```python
from typing import Optional
from auth import api_key_provider, auth_token_provider, oauth_provider
from auth.token_type import TokenType
from db.database import AppDatabase
from exception.client_error import ClientError
from fastapi import Depends, Header, Request, status
from fastapi.security import APIKeyHeader, HTTPBearer
from fastapi.security.http import HTTPAuthorizationCredentials
from pymongo.database import Database

def AuthProvider(
    request: Request,
    credentials_bearer: Optional[HTTPAuthorizationCredentials] = Depends(
        HTTPBearer(auto_error=False)
    ),
    credentials_key: Optional[str] = Depends(APIKeyHeader(name="Authorization")),
    x_auth_source: TokenType = Header(default=TokenType.API_KEY),
    db: Database = Depends(AppDatabase),
):
    """Enhanced AuthProvider with OAuth support."""
    match x_auth_source:
        case TokenType.AUTH_TOKEN:
            if not credentials_bearer:
                raise ClientError(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    message="Not authenticated",
                )
            
            validate_status = auth_token_provider.validate_credentials(
                credentials_bearer.credentials, request, db
            )
        
        case TokenType.API_KEY:
            if not credentials_key:
                raise ClientError(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    message="Not authenticated",
                )
            
            validate_status = api_key_provider.validate_credentials(
                credentials_key, request, db
            )
        
        case TokenType.OAUTH_TOKEN:  # New OAuth case
            if not credentials_bearer:
                raise ClientError(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    message="Not authenticated",
                )
            
            validate_status = oauth_provider.validate_credentials(
                credentials_bearer.credentials, request, db
            )
    
    if not validate_status:
        raise ClientError(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Not authenticated",
        )
```

---

## 3. Email Verification Strategy

### 3.1 Recommended Approach: Hybrid Verification

Based on the analysis of the existing email verification system and Google OAuth2 capabilities, here's the recommended strategy:

#### Strategy Overview
1. **Trust Google's Email Verification**: For Google OAuth users with `verified_email: true`
2. **Fallback to Dhruva Verification**: For Google OAuth users with `verified_email: false`
3. **Maintain Existing System**: Keep current email verification for traditional users

#### Implementation Logic
```python
def should_verify_email(google_user_info: GoogleUserInfo, existing_user: Optional[User] = None) -> bool:
    """Determine if email verification is needed for Google OAuth user."""
    
    # Trust Google's verification if email is verified
    if google_user_info.verified_email:
        return False
    
    # If user already exists and email is verified in our system, trust it
    if existing_user and existing_user.email_verified:
        return False
    
    # Otherwise, require our email verification
    return True
```

### 3.2 Email Verification Integration

#### Enhanced Email Verification Service
Update `server/module/auth/service/email_verification_service.py` to handle OAuth users:

```python
async def handle_oauth_email_verification(
    self,
    google_user_info: GoogleUserInfo,
    existing_user: Optional[User] = None
) -> Tuple[bool, Optional[str]]:
    """
    Handle email verification for Google OAuth users.
    
    Returns:
        Tuple[bool, Optional[str]]: (needs_verification, verification_token)
    """
    
    # Check if email verification is needed
    needs_verification = should_verify_email(google_user_info, existing_user)
    
    if not needs_verification:
        return False, None
    
    # Create pending verification for OAuth user
    verification_token = self.generate_verification_token()
    
    # Store OAuth user info in pending registrations with special flag
    pending_registration = PendingRegistration(
        email=google_user_info.email,
        name=google_user_info.name,
        password_hash=None,  # No password for OAuth users
        verification_token=verification_token,
        created_at=datetime.utcnow(),
        expires_at=self.generate_token_expiration(),
        verification_attempts=0,
        oauth_provider="google",
        oauth_user_id=google_user_info.id,
        oauth_verified_email=google_user_info.verified_email
    )
    
    self.pending_registration_repository.insert_one(pending_registration)
    
    # Send verification email
    await self.email_service.send_verification_email(
        to_email=google_user_info.email,
        name=google_user_info.name,
        verification_token=verification_token
    )
    
    return True, verification_token
```

### 3.3 Enhanced Pending Registration Model

#### Update Pending Registration Model
Update `server/module/auth/model/pending_registration.py`:

```python
from datetime import datetime
from typing import Optional
from db.MongoBaseModel import MongoBaseModel
from pydantic import EmailStr

class PendingRegistration(MongoBaseModel):
    """Enhanced pending registration with OAuth support."""
    email: EmailStr
    name: str
    password_hash: Optional[str] = None  # None for OAuth users
    verification_token: str
    created_at: datetime
    expires_at: datetime
    verification_attempts: int = 0
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    # OAuth-specific fields
    oauth_provider: Optional[str] = None  # "google", "github", etc.
    oauth_user_id: Optional[str] = None
    oauth_verified_email: Optional[bool] = None
    oauth_access_token: Optional[str] = None  # Encrypted
    oauth_refresh_token: Optional[str] = None  # Encrypted
    
    def is_oauth_registration(self) -> bool:
        """Check if this is an OAuth registration."""
        return self.oauth_provider is not None
    
    def is_traditional_registration(self) -> bool:
        """Check if this is a traditional email/password registration."""
        return self.password_hash is not None
```

---

## 4. Environment Configuration

### 4.1 Development Environment Variables

#### Add to `.env` file:
```bash
# Google OAuth2 Configuration
GOOGLE_OAUTH_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=your-google-client-secret
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/auth/oauth/google/callback

# OAuth2 Security
OAUTH_STATE_SECRET_KEY=your-oauth-state-secret-key-here
OAUTH_TOKEN_ENCRYPTION_KEY=your-32-byte-encryption-key-here

# Frontend URLs for OAuth
FRONTEND_BASE_URL=http://localhost:3000
OAUTH_SUCCESS_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/success
OAUTH_ERROR_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/error

# OAuth2 Settings
OAUTH_STATE_EXPIRY_MINUTES=10
OAUTH_TOKEN_REFRESH_THRESHOLD_MINUTES=30

# Existing JWT Configuration (keep existing)
JWT_SECRET_KEY=your-existing-jwt-secret-key

# Database Configuration (keep existing)
MONGO_APP_DB_USERNAME=dhruvaadmin
MONGO_APP_DB_PASSWORD=dhruva123
APP_DB_NAME=admin
APP_DB_CONNECTION_STRING=mongodb://${MONGO_APP_DB_USERNAME}:${MONGO_APP_DB_PASSWORD}@dhruva-platform-app-db:27017/admin?authSource=admin

# Redis Configuration (keep existing)
REDIS_HOST=dhruva-platform-redis
REDIS_PORT=6379
REDIS_PASSWORD=dhruva123
REDIS_DB=0
REDIS_SECURE=false
```

### 4.2 Production Environment Variables

#### Production `.env` configuration:
```bash
# Google OAuth2 Configuration (Production)
GOOGLE_OAUTH_CLIENT_ID=your-production-google-client-id.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://api.your-domain.com/auth/oauth/google/callback

# OAuth2 Security (Production)
OAUTH_STATE_SECRET_KEY=your-production-oauth-state-secret-key
OAUTH_TOKEN_ENCRYPTION_KEY=your-production-32-byte-encryption-key

# Frontend URLs for OAuth (Production)
FRONTEND_BASE_URL=https://your-domain.com
OAUTH_SUCCESS_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/success
OAUTH_ERROR_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/error

# Production Security Settings
OAUTH_REQUIRE_HTTPS=true
OAUTH_SECURE_COOKIES=true
OAUTH_SAME_SITE_COOKIES=strict
```

### 4.3 Environment Variable Descriptions

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `GOOGLE_OAUTH_CLIENT_ID` | Google OAuth2 client ID | `123456789.apps.googleusercontent.com` | Yes |
| `GOOGLE_OAUTH_CLIENT_SECRET` | Google OAuth2 client secret | `GOCSPX-abcdef123456` | Yes |
| `GOOGLE_OAUTH_REDIRECT_URI` | OAuth callback URI | `http://localhost:8000/auth/oauth/google/callback` | Yes |
| `OAUTH_STATE_SECRET_KEY` | Secret for OAuth state encryption | `your-secret-key-here` | Yes |
| `OAUTH_TOKEN_ENCRYPTION_KEY` | Key for encrypting OAuth tokens | `32-byte-key-for-aes-encryption` | Yes |
| `FRONTEND_BASE_URL` | Frontend application URL | `http://localhost:3000` | Yes |
| `OAUTH_SUCCESS_REDIRECT_URL` | Success redirect URL | `${FRONTEND_BASE_URL}/oauth/success` | No |
| `OAUTH_ERROR_REDIRECT_URL` | Error redirect URL | `${FRONTEND_BASE_URL}/oauth/error` | No |

### 4.4 Secure Credential Storage

#### Development Setup
1. **Never commit credentials to version control**
2. **Use environment files**: Store in `.env` file (add to `.gitignore`)
3. **Use different credentials**: Separate dev/prod credentials

#### Production Setup
1. **Environment Variables**: Use container environment variables
2. **Secret Management**: Consider AWS Secrets Manager, HashiCorp Vault
3. **Encryption**: Encrypt OAuth tokens at rest
4. **Rotation**: Regular credential rotation policy

---

## 5. Testing and Validation

### 5.1 End-to-End Testing

#### Test Script for Complete OAuth Flow
Create `tests/test_google_oauth_flow.py`:

```python
import pytest
import httpx
from fastapi.testclient import TestClient
from main import app

class TestGoogleOAuthFlow:
    """Test Google OAuth2 integration end-to-end."""
    
    def setup_method(self):
        self.client = TestClient(app)
    
    def test_oauth_login_initiation(self):
        """Test OAuth login URL generation."""
        response = self.client.get("/auth/oauth/google/login")
        
        assert response.status_code == 302
        assert "accounts.google.com" in response.headers["location"]
        assert "code_challenge" in response.headers["location"]
        assert "state" in response.headers["location"]
    
    def test_oauth_callback_with_valid_code(self):
        """Test OAuth callback with valid authorization code."""
        # This would require mocking Google's token endpoint
        # Implementation depends on your testing strategy
        pass
    
    def test_oauth_callback_with_invalid_state(self):
        """Test OAuth callback with invalid state parameter."""
        response = self.client.get(
            "/auth/oauth/google/callback?code=test&state=invalid"
        )
        
        assert response.status_code == 400
        assert "Invalid state parameter" in response.json()["detail"]["message"]
    
    def test_user_creation_from_oauth(self):
        """Test user creation from Google OAuth data."""
        # Mock Google user info and test user creation
        pass
    
    def test_existing_user_oauth_linking(self):
        """Test linking OAuth to existing user account."""
        # Test account linking functionality
        pass
```

### 5.2 Security Validation

#### Security Test Checklist
```python
def test_pkce_implementation():
    """Verify PKCE is properly implemented."""
    # Test code verifier generation
    # Test code challenge creation
    # Test code verifier validation
    pass

def test_state_parameter_validation():
    """Verify state parameter prevents CSRF."""
    # Test state generation
    # Test state validation
    # Test invalid state rejection
    pass

def test_token_encryption():
    """Verify OAuth tokens are encrypted at rest."""
    # Test token encryption
    # Test token decryption
    # Test encrypted storage
    pass

def test_redirect_uri_validation():
    """Verify redirect URI validation."""
    # Test valid redirect URIs
    # Test invalid redirect URI rejection
    # Test redirect URI tampering protection
    pass
```

### 5.3 Backward Compatibility Testing

#### Compatibility Test Suite
```python
def test_existing_jwt_authentication():
    """Ensure existing JWT auth still works."""
    # Test traditional login flow
    # Test JWT token validation
    # Test session management
    pass

def test_existing_api_key_authentication():
    """Ensure existing API key auth still works."""
    # Test API key validation
    # Test API key endpoints
    # Test inference with API keys
    pass

def test_mixed_authentication_scenarios():
    """Test scenarios with multiple auth methods."""
    # Test user with both password and OAuth
    # Test API key generation for OAuth users
    # Test session management across auth types
    pass
```

### 5.4 User Creation and API Key Testing

#### User Management Tests
```python
def test_oauth_user_creation():
    """Test OAuth user creation with API key generation."""
    # Mock Google OAuth flow
    # Verify user creation
    # Verify default API key generation
    # Verify role assignment (CONSUMER)
    pass

def test_oauth_user_without_password():
    """Test OAuth-only user functionality."""
    # Create OAuth-only user
    # Verify no password field
    # Verify OAuth provider info
    # Verify API key functionality
    pass

def test_email_verification_for_oauth():
    """Test email verification for unverified OAuth emails."""
    # Mock unverified Google email
    # Verify email verification trigger
    # Test verification completion
    # Verify user activation
    pass
```

---

## 6. OAuth Endpoints Implementation

### 6.1 OAuth Router

#### Create OAuth Router
Create `server/module/auth/router/oauth_router.py`:

```python
import secrets
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import RedirectResponse
from exception.client_error import ClientErrorResponse
from module.auth.service.google_oauth_service import GoogleOAuthService
from module.auth.service.oauth_user_service import OAuthUserService
from module.auth.model.oauth_state import OAuthState
from module.auth.repository.oauth_state_repository import OAuthStateRepository
from schema.auth.response.oauth_response import (
    OAuthLoginResponse,
    OAuthCallbackResponse
)

router = APIRouter(
    prefix="/oauth",
    responses={"401": {"model": ClientErrorResponse}},
)

@router.get("/google/login")
async def google_oauth_login(
    request: Request,
    redirect_uri: Optional[str] = Query(None),
    google_service: GoogleOAuthService = Depends(GoogleOAuthService),
    oauth_state_repo: OAuthStateRepository = Depends(OAuthStateRepository)
):
    """
    Initiate Google OAuth2 login flow.

    Query Parameters:
    - redirect_uri: Optional custom redirect URI after successful auth
    """
    try:
        # Generate PKCE parameters
        code_verifier, code_challenge = google_service.generate_pkce_pair()

        # Generate state parameter for CSRF protection
        state = secrets.token_urlsafe(32)

        # Store OAuth state temporarily
        oauth_state = OAuthState(
            state=state,
            code_verifier=code_verifier,
            code_challenge=code_challenge,
            provider="google",
            redirect_uri=redirect_uri or request.headers.get("referer", "/"),
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(minutes=10)
        )

        oauth_state_repo.insert_one(oauth_state)

        # Generate authorization URL
        auth_url = google_service.generate_authorization_url(
            state=state,
            code_challenge=code_challenge
        )

        # Redirect to Google OAuth
        return RedirectResponse(url=auth_url, status_code=302)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate OAuth flow: {str(e)}"
        )

@router.get("/google/callback")
async def google_oauth_callback(
    code: str = Query(...),
    state: str = Query(...),
    error: Optional[str] = Query(None),
    google_service: GoogleOAuthService = Depends(GoogleOAuthService),
    oauth_user_service: OAuthUserService = Depends(OAuthUserService),
    oauth_state_repo: OAuthStateRepository = Depends(OAuthStateRepository)
):
    """
    Handle Google OAuth2 callback.

    Query Parameters:
    - code: Authorization code from Google
    - state: State parameter for CSRF protection
    - error: Error from Google (if any)
    """
    try:
        # Handle OAuth errors
        if error:
            error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error={error}"
            return RedirectResponse(url=error_url, status_code=302)

        # Validate and retrieve OAuth state
        oauth_state = oauth_state_repo.find_one({"state": state})
        if not oauth_state:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired state parameter"
            )

        # Exchange code for tokens
        tokens = await google_service.exchange_code_for_tokens(
            code=code,
            code_verifier=oauth_state.code_verifier
        )

        # Get user info from Google
        user_info = await google_service.get_user_info(tokens.access_token)

        # Create or update user
        user, jwt_token = await oauth_user_service.handle_oauth_user(
            provider="google",
            user_info=user_info,
            tokens=tokens
        )

        # Clean up OAuth state
        oauth_state_repo.delete_by_id(oauth_state.id)

        # Redirect to success page with token
        success_url = f"{os.environ.get('OAUTH_SUCCESS_REDIRECT_URL', '/')}?token={jwt_token}"
        return RedirectResponse(url=success_url, status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error=server_error"
        return RedirectResponse(url=error_url, status_code=302)

@router.post("/google/link")
async def link_google_account(
    code: str,
    state: str,
    request_session: RequestSession = Depends(InjectRequestSession),
    google_service: GoogleOAuthService = Depends(GoogleOAuthService),
    oauth_user_service: OAuthUserService = Depends(OAuthUserService),
    oauth_state_repo: OAuthStateRepository = Depends(OAuthStateRepository)
):
    """
    Link Google OAuth account to existing authenticated user.

    Requires existing authentication (JWT token).
    """
    try:
        # Validate OAuth state
        oauth_state = oauth_state_repo.find_one({"state": state})
        if not oauth_state:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired state parameter"
            )

        # Exchange code for tokens
        tokens = await google_service.exchange_code_for_tokens(
            code=code,
            code_verifier=oauth_state.code_verifier
        )

        # Get user info from Google
        user_info = await google_service.get_user_info(tokens.access_token)

        # Link OAuth account to existing user
        updated_user = await oauth_user_service.link_oauth_account(
            user_id=request_session.id,
            provider="google",
            user_info=user_info,
            tokens=tokens
        )

        # Clean up OAuth state
        oauth_state_repo.delete_by_id(oauth_state.id)

        return {
            "message": "Google account linked successfully",
            "provider": "google",
            "email": user_info.email
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to link Google account: {str(e)}"
        )

@router.delete("/google/unlink")
async def unlink_google_account(
    request_session: RequestSession = Depends(InjectRequestSession),
    oauth_user_service: OAuthUserService = Depends(OAuthUserService)
):
    """
    Unlink Google OAuth account from authenticated user.

    Requires existing authentication (JWT token).
    """
    try:
        updated_user = await oauth_user_service.unlink_oauth_account(
            user_id=request_session.id,
            provider="google"
        )

        return {
            "message": "Google account unlinked successfully",
            "provider": "google"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to unlink Google account: {str(e)}"
        )
```

### 6.2 OAuth User Service

#### Create OAuth User Service
Create `server/module/auth/service/oauth_user_service.py`:

```python
import os
import jwt
from datetime import datetime, timedelta
from typing import Tuple, Optional
from fastapi import HTTPException, status, Depends
from bson.objectid import ObjectId
from module.auth.model.user import User, OAuthProvider
from module.auth.repository.user_repository import UserRepository
from module.auth.service.auth_service import AuthService
from module.auth.service.email_verification_service import EmailVerificationService
from module.auth.service.google_oauth_service import GoogleUserInfo, GoogleOAuthTokens
from schema.auth.common import RoleType, ApiKeyType
from schema.auth.request import CreateApiKeyRequest
from utils.encryption import encrypt_token, decrypt_token

class OAuthUserService:
    """Service for managing OAuth users and account linking."""

    def __init__(
        self,
        user_repository: UserRepository = Depends(UserRepository),
        auth_service: AuthService = Depends(AuthService),
        email_verification_service: EmailVerificationService = Depends(EmailVerificationService)
    ):
        self.user_repository = user_repository
        self.auth_service = auth_service
        self.email_verification_service = email_verification_service

    async def handle_oauth_user(
        self,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> Tuple[User, str]:
        """
        Handle OAuth user login/registration.

        Returns:
            Tuple[User, str]: (user_object, jwt_token)
        """
        # Check if user exists by email
        existing_user = self.user_repository.find_one({"email": user_info.email})

        if existing_user:
            # Update existing user with OAuth info
            user = await self._update_existing_user_oauth(
                existing_user, provider, user_info, tokens
            )
        else:
            # Create new OAuth user
            user = await self._create_new_oauth_user(
                provider, user_info, tokens
            )

        # Generate JWT token for session
        jwt_token = self._generate_oauth_jwt_token(user, provider)

        # Update last login
        user.last_login = datetime.utcnow()
        self.user_repository.save(user)

        return user, jwt_token

    async def _create_new_oauth_user(
        self,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Create new user from OAuth information."""

        # Check if email verification is needed
        needs_verification = not user_info.verified_email

        if needs_verification:
            # Create pending registration for unverified OAuth email
            verification_token = await self.email_verification_service.handle_oauth_email_verification(
                user_info, None
            )
            raise HTTPException(
                status_code=status.HTTP_202_ACCEPTED,
                detail={
                    "message": "Email verification required",
                    "email": user_info.email,
                    "verification_required": True
                }
            )

        # Create OAuth provider info
        oauth_provider = OAuthProvider(
            provider=provider,
            provider_user_id=user_info.id,
            email=user_info.email,
            verified_email=user_info.verified_email,
            access_token=encrypt_token(tokens.access_token),
            refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
            token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
            scope=tokens.scope,
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )

        # Create new user
        new_user = User(
            name=user_info.name,
            email=user_info.email,
            password=None,  # OAuth-only user
            role=RoleType.CONSUMER,
            oauth_providers=[oauth_provider],
            created_via=provider,
            email_verified=user_info.verified_email,
            created_at=datetime.utcnow()
        )

        # Save user
        user_id = self.user_repository.insert_one(new_user)
        created_user = self.user_repository.get_by_id(ObjectId(str(user_id)))

        # Generate default API key
        api_request = CreateApiKeyRequest(
            name="default",
            type=ApiKeyType.INFERENCE,
            regenerate=False,
            target_user_id=str(created_user.id),
            data_tracking=False,
        )

        self.auth_service.create_api_key(
            request=api_request,
            id=ObjectId(str(created_user.id)),
        )

        return created_user

    async def _update_existing_user_oauth(
        self,
        existing_user: User,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Update existing user with OAuth information."""

        # Check if user already has this OAuth provider
        existing_oauth = existing_user.get_oauth_provider(provider)

        if existing_oauth:
            # Update existing OAuth provider info
            existing_oauth.access_token = encrypt_token(tokens.access_token)
            existing_oauth.refresh_token = encrypt_token(tokens.refresh_token) if tokens.refresh_token else None
            existing_oauth.token_expires_at = datetime.utcnow() + timedelta(seconds=tokens.expires_in)
            existing_oauth.scope = tokens.scope
            existing_oauth.last_login = datetime.utcnow()
            existing_oauth.verified_email = user_info.verified_email
        else:
            # Add new OAuth provider
            oauth_provider = OAuthProvider(
                provider=provider,
                provider_user_id=user_info.id,
                email=user_info.email,
                verified_email=user_info.verified_email,
                access_token=encrypt_token(tokens.access_token),
                refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
                token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
                scope=tokens.scope,
                created_at=datetime.utcnow(),
                last_login=datetime.utcnow()
            )
            existing_user.oauth_providers.append(oauth_provider)

        # Update email verification status if OAuth email is verified
        if user_info.verified_email and not existing_user.email_verified:
            existing_user.email_verified = True

        # Save updated user
        self.user_repository.save(existing_user)

        return existing_user

    def _generate_oauth_jwt_token(self, user: User, provider: str) -> str:
        """Generate JWT token for OAuth user session."""

        payload = {
            "sub": str(user.id),
            "name": user.name,
            "email": user.email,
            "role": user.role,
            "auth_type": "oauth",
            "oauth_provider": provider,
            "exp": datetime.utcnow() + timedelta(days=30),
            "iat": datetime.utcnow(),
        }

        token = jwt.encode(
            payload,
            os.environ["JWT_SECRET_KEY"],
            algorithm="HS256",
            headers={"tok": "oauth"}
        )

        return token

    async def link_oauth_account(
        self,
        user_id: ObjectId,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Link OAuth account to existing user."""

        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Check if user already has this provider
        if user.has_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{provider.title()} account already linked"
            )

        # Check if OAuth email matches user email
        if user.email != user_info.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OAuth email does not match account email"
            )

        # Add OAuth provider
        oauth_provider = OAuthProvider(
            provider=provider,
            provider_user_id=user_info.id,
            email=user_info.email,
            verified_email=user_info.verified_email,
            access_token=encrypt_token(tokens.access_token),
            refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
            token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
            scope=tokens.scope,
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )

        user.oauth_providers.append(oauth_provider)

        # Update email verification if OAuth email is verified
        if user_info.verified_email and not user.email_verified:
            user.email_verified = True

        self.user_repository.save(user)
        return user

    async def unlink_oauth_account(self, user_id: ObjectId, provider: str) -> User:
        """Unlink OAuth account from user."""

        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Check if user has this provider
        if not user.has_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{provider.title()} account not linked"
            )

        # Check if user would be left without authentication method
        if user.is_oauth_only() and len(user.oauth_providers) == 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot unlink last authentication method. Please set a password first."
            )

        # Remove OAuth provider
        user.oauth_providers = [
            oauth for oauth in user.oauth_providers
            if oauth.provider != provider
        ]

        self.user_repository.save(user)
        return user
```

### 6.3 OAuth Response Schemas

#### Create OAuth Response Schemas
Create `server/schema/auth/response/oauth_response.py`:

```python
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr

class OAuthLoginResponse(BaseModel):
    """Response for OAuth login initiation."""
    authorization_url: str
    state: str
    expires_at: datetime

class OAuthCallbackResponse(BaseModel):
    """Response for OAuth callback."""
    message: str
    user_id: str
    email: EmailStr
    name: str
    role: str
    token: str
    provider: str
    is_new_user: bool
    email_verified: bool

class OAuthLinkResponse(BaseModel):
    """Response for OAuth account linking."""
    message: str
    provider: str
    email: EmailStr
    linked_at: datetime

class OAuthUnlinkResponse(BaseModel):
    """Response for OAuth account unlinking."""
    message: str
    provider: str
    unlinked_at: datetime

class OAuthUserInfoResponse(BaseModel):
    """OAuth user information response."""
    provider: str
    provider_user_id: str
    email: EmailStr
    verified_email: bool
    linked_at: datetime
    last_login: Optional[datetime] = None
```

---

## 7. Security Implementation

### 7.1 Token Encryption Utility

#### Create Encryption Utility
Create `server/utils/encryption.py`:

```python
import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class TokenEncryption:
    """Utility for encrypting/decrypting OAuth tokens."""

    def __init__(self):
        # Get encryption key from environment
        key = os.environ.get("OAUTH_TOKEN_ENCRYPTION_KEY")
        if not key:
            raise ValueError("OAUTH_TOKEN_ENCRYPTION_KEY not configured")

        # Derive Fernet key from the provided key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'dhruva_oauth_salt',  # Use consistent salt
            iterations=100000,
        )
        key_bytes = base64.urlsafe_b64encode(kdf.derive(key.encode()))
        self.fernet = Fernet(key_bytes)

    def encrypt(self, token: str) -> str:
        """Encrypt a token."""
        if not token:
            return ""

        encrypted_bytes = self.fernet.encrypt(token.encode())
        return base64.urlsafe_b64encode(encrypted_bytes).decode()

    def decrypt(self, encrypted_token: str) -> str:
        """Decrypt a token."""
        if not encrypted_token:
            return ""

        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_token.encode())
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception:
            raise ValueError("Failed to decrypt token")

# Global instance
_token_encryption = None

def get_token_encryption() -> TokenEncryption:
    """Get global token encryption instance."""
    global _token_encryption
    if _token_encryption is None:
        _token_encryption = TokenEncryption()
    return _token_encryption

def encrypt_token(token: str) -> str:
    """Encrypt a token using global encryption instance."""
    return get_token_encryption().encrypt(token)

def decrypt_token(encrypted_token: str) -> str:
    """Decrypt a token using global encryption instance."""
    return get_token_encryption().decrypt(encrypted_token)
```

### 7.2 OAuth State Repository

#### Create OAuth State Repository
Create `server/module/auth/repository/oauth_state_repository.py`:

```python
from typing import Optional
from bson.objectid import ObjectId
from module.auth.model.oauth_state import OAuthState
from db.repository import Repository

class OAuthStateRepository(Repository[OAuthState]):
    """Repository for OAuth state management."""

    def __init__(self):
        super().__init__(OAuthState, "oauth_state")

    def find_by_state(self, state: str) -> Optional[OAuthState]:
        """Find OAuth state by state parameter."""
        return self.find_one({"state": state})

    def delete_by_state(self, state: str) -> bool:
        """Delete OAuth state by state parameter."""
        result = self.collection.delete_one({"state": state})
        return result.deleted_count > 0

    def cleanup_expired_states(self) -> int:
        """Clean up expired OAuth states."""
        from datetime import datetime
        result = self.collection.delete_many({
            "expires_at": {"$lt": datetime.utcnow()}
        })
        return result.deleted_count
```

### 7.3 Enhanced AuthProvider with OAuth

#### Update AuthProvider Import
Update `server/auth/__init__.py`:

```python
from . import api_key_provider, auth_token_provider
from .oauth_provider import oauth_provider

__all__ = ["api_key_provider", "auth_token_provider", "oauth_provider"]
```

### 7.4 Security Middleware

#### Create OAuth Security Middleware
Create `server/middleware/oauth_security_middleware.py`:

```python
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

class OAuthSecurityMiddleware(BaseHTTPMiddleware):
    """Security middleware for OAuth endpoints."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Add security headers for OAuth endpoints
        if request.url.path.startswith("/auth/oauth/"):
            # Ensure HTTPS in production
            if (os.environ.get("OAUTH_REQUIRE_HTTPS", "false").lower() == "true"
                and request.url.scheme != "https"):
                return Response(
                    content="HTTPS required for OAuth endpoints",
                    status_code=400
                )

            # Add security headers
            response = await call_next(request)
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

            return response

        return await call_next(request)
```

---

## 8. Troubleshooting

### 8.1 Common Issues and Solutions

#### Issue 1: "Invalid redirect URI" Error
**Symptoms**: Google returns error about redirect URI mismatch
**Solutions**:
1. Verify redirect URI in Google Cloud Console matches exactly
2. Check for trailing slashes, HTTP vs HTTPS
3. Ensure development and production URIs are both configured
4. Verify environment variable `GOOGLE_OAUTH_REDIRECT_URI` is correct

#### Issue 2: "Invalid state parameter" Error
**Symptoms**: OAuth callback fails with state validation error
**Solutions**:
1. Check OAuth state storage and retrieval
2. Verify state expiration (default 10 minutes)
3. Ensure database connection for OAuth state collection
4. Check for race conditions in state cleanup

#### Issue 3: Token Encryption Errors
**Symptoms**: Failed to encrypt/decrypt OAuth tokens
**Solutions**:
1. Verify `OAUTH_TOKEN_ENCRYPTION_KEY` is set and 32+ characters
2. Check encryption key consistency across restarts
3. Ensure cryptography library is installed: `pip install cryptography`
4. Verify base64 encoding/decoding

#### Issue 4: Email Verification Loop
**Symptoms**: OAuth users stuck in email verification
**Solutions**:
1. Check Google user info `verified_email` field
2. Verify email verification logic for OAuth users
3. Ensure pending registration cleanup
4. Check email service configuration

### 8.2 Debug Configuration

#### Enable Debug Logging
Add to your logging configuration:

```python
import logging

# Enable OAuth debug logging
logging.getLogger("module.auth.service.google_oauth_service").setLevel(logging.DEBUG)
logging.getLogger("module.auth.service.oauth_user_service").setLevel(logging.DEBUG)
logging.getLogger("auth.oauth_provider").setLevel(logging.DEBUG)

# Enable HTTP client logging for OAuth requests
logging.getLogger("httpx").setLevel(logging.DEBUG)
```

#### Debug Environment Variables
```bash
# Add to .env for debugging
OAUTH_DEBUG_MODE=true
OAUTH_LOG_TOKENS=false  # Never log actual tokens in production
OAUTH_LOG_USER_INFO=true
```

### 8.3 Testing Checklist

#### Pre-Deployment Checklist
- [ ] Google Cloud Console project configured
- [ ] OAuth consent screen approved
- [ ] Client ID and secret configured
- [ ] Redirect URIs whitelisted
- [ ] Environment variables set
- [ ] Database collections created
- [ ] Encryption keys generated
- [ ] HTTPS configured (production)
- [ ] Security headers implemented
- [ ] Error handling tested
- [ ] Token refresh tested
- [ ] Account linking tested
- [ ] Email verification tested

#### Production Deployment Checklist
- [ ] Production OAuth credentials
- [ ] Production redirect URIs
- [ ] HTTPS enforcement enabled
- [ ] Secure cookie settings
- [ ] Token encryption verified
- [ ] Rate limiting configured
- [ ] Monitoring and alerting set up
- [ ] Backup and recovery tested
- [ ] Security audit completed
- [ ] User documentation updated

---

## Conclusion

This comprehensive implementation guide provides everything needed to integrate Google OAuth2 into the Dhruva Platform as the first step of the OAuth2 roadmap. The implementation follows security best practices, maintains backward compatibility, and provides a solid foundation for adding additional OAuth providers.

### Key Implementation Highlights

1. **Security First**: PKCE implementation, state validation, token encryption
2. **Backward Compatibility**: Existing authentication methods remain unchanged
3. **Flexible Email Verification**: Hybrid approach trusting Google's verification with fallback
4. **Comprehensive Error Handling**: Robust error handling and user feedback
5. **Production Ready**: Environment configuration for development and production

### Next Steps

1. **Implement the code**: Follow the implementation steps in order
2. **Test thoroughly**: Use the provided test cases and checklists
3. **Deploy gradually**: Use feature flags for controlled rollout
4. **Monitor closely**: Set up monitoring and alerting for OAuth flows
5. **Gather feedback**: Collect user feedback for improvements

After successful Google OAuth2 implementation, you can follow the same patterns to add GitHub and Microsoft OAuth2 support, completing the full OAuth2 integration roadmap outlined in the analysis document.
