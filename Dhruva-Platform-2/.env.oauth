# Google OAuth2 Configuration - ACTUAL CREDENTIALS
GOOGLE_OAUTH_CLIENT_ID=235810295758-lhf7ekmbta2hjp0dd580lvm0301o1lue.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=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
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/auth/oauth/google/callback

# OAuth2 Security Keys (Generated)
OAUTH_STATE_SECRET_KEY=oauth-state-secret-key-dhruva-platform-2024
OAUTH_TOKEN_ENCRYPTION_KEY=oauth-token-encryption-key-dhruva-platform-32-chars

# Frontend URLs for OAuth Redirects
FRONTEND_BASE_URL=http://localhost:3001
OAUTH_SUCCESS_REDIRECT_URL=http://localhost:3001/oauth/success
OAUTH_ERROR_REDIRECT_URL=http://localhost:3001/oauth/error

# OAuth2 Settings
OAUTH_STATE_EXPIRY_MINUTES=10
OAUTH_TOKEN_REFRESH_THRESHOLD_MINUTES=30
OAUTH_REQUIRE_HTTPS=false
OAUTH_SECURE_COOKIES=false
OAUTH_SAME_SITE_COOKIES=lax

# Existing JWT Configuration (keep your existing key)
JWT_SECRET_KEY=your-existing-jwt-secret-key

# Database Configuration (keep existing)
MONGO_APP_DB_USERNAME=dhruvaadmin
MONGO_APP_DB_PASSWORD=dhruva123
APP_DB_NAME=admin
APP_DB_CONNECTION_STRING=***********************************************************************************

# Redis Configuration (keep existing)
REDIS_HOST=dhruva-platform-redis
REDIS_PORT=6379
REDIS_PASSWORD=dhruva123
REDIS_DB=0
REDIS_SECURE=false
