# Google OAuth2 Credentials Setup Guide

## ⚠️ Important: API Key vs OAuth2 Credentials

The Google API key you provided (`AIzaSyCjGJE2K3geNQJAel0pmkhZ6f5e8cDLjk4`) is **NOT sufficient** for OAuth2 integration.

### What You Have vs What You Need

| What You Have | What You Need |
|---------------|---------------|
| **Google API Key** | **OAuth2 Client Credentials** |
| `AIzaSyCjGJE2K3geNQJAel0pmkhZ6f5e8cDLjk4` | Client ID + Client Secret |
| Server-to-server API calls | User authentication flows |
| No user context | Full user authorization |

## 🎯 **Required OAuth2 Credentials**

For Google OAuth2 integration, you need:

1. **OAuth2 Client ID** (format: `*********.apps.googleusercontent.com`)
2. **OAuth2 Client Secret** (format: `GOCSPX-abcdef123456`)
3. **Configured Redirect URIs**
4. **OAuth Consent Screen**

---

## 📋 **Step-by-Step Setup Instructions**

### Step 1: Access Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Sign in with your Google account
3. Select your existing project or create a new one

### Step 2: Enable Required APIs

1. Navigate to **"APIs & Services" → "Library"**
2. Search for and enable these APIs:
   - **Google+ API** (for user profile information)
   - **Google Identity** (for OpenID Connect)
   - **People API** (optional, for extended profile data)

### Step 3: Configure OAuth Consent Screen

1. Go to **"APIs & Services" → "OAuth consent screen"**
2. Choose **"External"** user type (unless you have Google Workspace)
3. Fill in the required information:

```
App name: Dhruva Platform
User support email: [<EMAIL>]
Developer contact information: [<EMAIL>]

App domain (optional):
- Application home page: https://your-domain.com
- Application privacy policy link: https://your-domain.com/privacy
- Application terms of service link: https://your-domain.com/terms

Authorized domains:
- your-domain.com (for production)
- localhost (for development)
```

4. **Scopes**: Add these OAuth scopes:
   - `openid`
   - `email` 
   - `profile`

5. **Test users** (for development):
   - Add your development email addresses
   - Add team member emails who will test the integration

6. Click **"Save and Continue"** through all steps

### Step 4: Create OAuth2 Client Credentials

1. Go to **"APIs & Services" → "Credentials"**
2. Click **"Create Credentials" → "OAuth client ID"**
3. Choose **"Web application"**
4. Configure the OAuth client:

```
Name: Dhruva Platform OAuth Client

Authorized JavaScript origins:
- http://localhost:3000 (development frontend)
- https://your-domain.com (production frontend)

Authorized redirect URIs:
- http://localhost:8000/auth/oauth/google/callback (development API)
- https://api.your-domain.com/auth/oauth/google/callback (production API)
```

5. Click **"Create"**
6. **IMPORTANT**: Copy and securely store:
   - **Client ID** (e.g., `*********.apps.googleusercontent.com`)
   - **Client Secret** (e.g., `GOCSPX-abcdef123456`)

### Step 5: Download Credentials (Optional)

1. Click the download button next to your OAuth2 client
2. Save the JSON file as `google-oauth-credentials.json`
3. **⚠️ NEVER commit this file to version control**

---

## 🔧 **Environment Configuration**

Once you have the OAuth2 credentials, configure your environment:

### Development Environment (.env)
```bash
# Google OAuth2 Credentials (REPLACE WITH YOUR ACTUAL CREDENTIALS)
GOOGLE_OAUTH_CLIENT_ID=your-actual-client-id.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=your-actual-client-secret
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/auth/oauth/google/callback

# OAuth2 Security
OAUTH_STATE_SECRET_KEY=your-oauth-state-secret-key-here
OAUTH_TOKEN_ENCRYPTION_KEY=your-32-byte-encryption-key-here

# Frontend URLs
FRONTEND_BASE_URL=http://localhost:3000
OAUTH_SUCCESS_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/success
OAUTH_ERROR_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/error

# Existing Configuration (keep these)
JWT_SECRET_KEY=your-existing-jwt-secret-key
MONGO_APP_DB_USERNAME=dhruvaadmin
MONGO_APP_DB_PASSWORD=dhruva123
# ... other existing variables
```

### Production Environment
```bash
# Google OAuth2 Credentials (Production)
GOOGLE_OAUTH_CLIENT_ID=your-production-client-id.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=your-production-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://api.your-domain.com/auth/oauth/google/callback

# Production Security
OAUTH_REQUIRE_HTTPS=true
OAUTH_SECURE_COOKIES=true
OAUTH_SAME_SITE_COOKIES=strict

# Frontend URLs (Production)
FRONTEND_BASE_URL=https://your-domain.com
OAUTH_SUCCESS_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/success
OAUTH_ERROR_REDIRECT_URL=${FRONTEND_BASE_URL}/oauth/error
```

---

## ✅ **Verification Steps**

After setup, verify your configuration:

### 1. Test OAuth Consent Screen
- Visit your OAuth consent screen in test mode
- Ensure all information is correct
- Test with a test user account

### 2. Verify Redirect URIs
- Ensure development and production URIs are correctly configured
- Check for exact matches (no trailing slashes unless intended)
- Verify HTTP vs HTTPS protocols

### 3. Test Scopes
- Confirm `openid`, `email`, and `profile` scopes are configured
- Test scope permissions with a test user

### 4. Validate Credentials
- Ensure Client ID format: `*.apps.googleusercontent.com`
- Ensure Client Secret format: `GOCSPX-*`
- Test credentials are not expired or revoked

---

## 🚨 **Security Best Practices**

### Credential Security
1. **Never expose credentials in client-side code**
2. **Use environment variables for all credentials**
3. **Rotate credentials regularly**
4. **Use different credentials for development and production**

### OAuth Security
1. **Always use HTTPS in production**
2. **Implement PKCE (Proof Key for Code Exchange)**
3. **Validate state parameters for CSRF protection**
4. **Encrypt OAuth tokens at rest**

### Access Control
1. **Limit OAuth scopes to minimum required**
2. **Regularly audit OAuth permissions**
3. **Monitor OAuth usage and errors**
4. **Implement proper session management**

---

## 🔄 **Next Steps**

Once you have obtained the proper OAuth2 credentials:

1. **Update environment variables** with actual Client ID and Client Secret
2. **Test the OAuth flow** in development environment
3. **Implement the complete OAuth2 integration** following the implementation guide
4. **Deploy to production** with production credentials
5. **Monitor and maintain** the OAuth integration

---

## 📞 **Support and Troubleshooting**

### Common Issues
- **"Invalid redirect URI"**: Check exact URI matches in Google Cloud Console
- **"Invalid client"**: Verify Client ID and Client Secret are correct
- **"Access denied"**: Check OAuth consent screen configuration
- **"Scope not authorized"**: Verify required scopes are configured

### Getting Help
- [Google OAuth2 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Google Cloud Console Support](https://cloud.google.com/support)
- [OAuth2 Troubleshooting Guide](https://developers.google.com/identity/protocols/oauth2/troubleshooting)

---

## ⏭️ **Ready to Implement?**

Once you have:
- ✅ OAuth2 Client ID
- ✅ OAuth2 Client Secret  
- ✅ Configured redirect URIs
- ✅ OAuth consent screen setup
- ✅ Environment variables configured

You can proceed with the complete Google OAuth2 implementation following the detailed implementation guide.
