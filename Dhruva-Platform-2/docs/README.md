# Dhruva Platform Documentation

Welcome to the comprehensive documentation for the Dhruva Platform - an AI/ML model serving platform for Indian language processing.

## 📚 Documentation Structure

This documentation is organized into module-specific guides and high-level architectural overviews:

### Module Documentation
- [Authentication & Authorization System](./modules/auth/README.md)
- [AI Services Module](./modules/services/README.md)
- [Metering & Analytics System](./modules/metering/README.md)
- [Frontend Application](./modules/frontend/README.md)
- [Database Layer](./modules/database/README.md)
- [Monitoring & Observability](./modules/monitoring/README.md)
- [Background Processing](./modules/celery/README.md)
- [API Gateway & Routing](./modules/api/README.md)

### Architecture Documentation
- [System Architecture Overview](./architecture/system-overview.md)
- [Data Flow Architecture](./architecture/data-flow.md)
- [Deployment Architecture](./architecture/deployment.md)
- [Security Architecture](./architecture/security.md)

### Operational Guides
- [Deployment Guide](./deployment/README.md)
- [Configuration Management](./configuration/README.md)
- [Monitoring & Alerting](./monitoring/README.md)
- [Troubleshooting Guide](./troubleshooting/README.md)

## 🚀 Quick Start

1. **System Overview**: Start with [System Architecture Overview](./architecture/system-overview.md)
2. **Deployment**: Follow the [Deployment Guide](./deployment/README.md)
3. **API Usage**: Check [API Gateway & Routing](./modules/api/README.md)
4. **Monitoring**: Set up [Monitoring & Observability](./modules/monitoring/README.md)

## 🏗️ Platform Overview

Dhruva Platform is a comprehensive AI/ML serving platform that provides:

- **Multi-language AI Services**: Translation, ASR, TTS, NER, and Transliteration
- **Scalable Architecture**: Microservices with Docker containerization
- **Real-time Processing**: Streaming capabilities for live audio processing
- **Comprehensive Metering**: Usage tracking and analytics
- **Enterprise Security**: Multi-factor authentication and authorization
- **Monitoring & Observability**: Full metrics and logging stack

## 🔧 Technology Stack

- **Backend**: Python, FastAPI, Celery
- **Frontend**: Next.js, TypeScript, Chakra UI
- **Databases**: MongoDB, TimescaleDB, Redis
- **Message Queue**: RabbitMQ
- **Monitoring**: Prometheus, Grafana
- **Deployment**: Docker, Docker Compose, Kubernetes
- **Authentication**: JWT, OAuth2, API Keys

## 📖 Documentation Conventions

- **🏗️ Architecture**: System design and component relationships
- **⚙️ Configuration**: Setup and configuration details
- **🔌 API**: Endpoint documentation and examples
- **📊 Monitoring**: Metrics, logging, and observability
- **🚀 Deployment**: Installation and deployment procedures
- **🔒 Security**: Authentication, authorization, and security measures

## 🤝 Contributing

For contributing to this documentation:

1. Follow the established structure and conventions
2. Include Mermaid diagrams for architectural components
3. Provide code examples and configuration snippets
4. Update the main README when adding new modules

## 📞 Support

For technical support and questions:
- Check the [Troubleshooting Guide](./troubleshooting/README.md)
- Review module-specific documentation
- Consult the architecture diagrams for system understanding
