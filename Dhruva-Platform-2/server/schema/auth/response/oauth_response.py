"""
OAuth response schemas for API responses.
Defines the structure of responses for OAuth-related endpoints.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr


class OAuthLoginResponse(BaseModel):
    """Response for OAuth login initiation."""
    authorization_url: str
    state: str
    provider: str
    expires_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthCallbackResponse(BaseModel):
    """Response for OAuth callback."""
    message: str
    user_id: str
    email: EmailStr
    name: str
    role: str
    token: str
    provider: str
    is_new_user: bool
    email_verified: bool
    created_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthLinkResponse(BaseModel):
    """Response for OAuth account linking."""
    message: str
    provider: str
    email: EmailStr
    linked_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthUnlinkResponse(BaseModel):
    """Response for OAuth account unlinking."""
    message: str
    provider: str
    unlinked_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthProviderInfo(BaseModel):
    """OAuth provider information."""
    provider: str
    provider_user_id: str
    email: EmailStr
    verified_email: bool
    linked_at: datetime
    last_login: Optional[datetime] = None
    scope: str
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthUserInfoResponse(BaseModel):
    """OAuth user information response."""
    user_id: str
    name: str
    email: EmailStr
    role: str
    email_verified: bool
    created_via: str
    oauth_providers: List[OAuthProviderInfo]
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthStatusResponse(BaseModel):
    """OAuth status response."""
    provider: str
    linked: bool
    user_id: str
    message: str
    provider_info: Optional[OAuthProviderInfo] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthErrorResponse(BaseModel):
    """OAuth error response."""
    error: str
    error_description: str
    provider: str
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAuthTokenRefreshResponse(BaseModel):
    """OAuth token refresh response."""
    message: str
    provider: str
    token_refreshed: bool
    expires_at: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
