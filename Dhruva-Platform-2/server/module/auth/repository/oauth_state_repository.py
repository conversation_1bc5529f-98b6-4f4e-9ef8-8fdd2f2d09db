"""
Repository for OAuth state management.
Handles temporary storage and retrieval of OAuth flow state parameters.
"""

from datetime import datetime
from typing import Optional
from bson.objectid import ObjectId
from fastapi import Depends
from pymongo.database import Database

from db.BaseRepository import BaseRepository
from db.database import AppDatabase
from module.auth.model.oauth_state import OAuthState


class OAuthStateRepository(BaseRepository[OAuthState]):
    """Repository for OAuth state management."""

    __collection_name__ = "oauth_state"

    def __init__(self, db: Database = Depends(AppDatabase)) -> None:
        super().__init__(db, self.__collection_name__)

    def insert_one(self, oauth_state: OAuthState) -> str:
        """
        Insert OAuth state into database.

        Args:
            oauth_state: OAuth state to insert

        Returns:
            Inserted document ID
        """
        result = self.collection.insert_one(oauth_state.dict())
        return str(result.inserted_id)
    
    def find_by_state(self, state: str) -> Optional[OAuthState]:
        """
        Find OAuth state by state parameter.

        Args:
            state: State parameter to search for

        Returns:
            OAuthState if found, None otherwise
        """
        oauth_state_doc = self.collection.find_one({"state": state})
        if oauth_state_doc:
            return OAuthState(**oauth_state_doc)
        return None
    
    def find_valid_state(self, state: str, provider: str) -> Optional[OAuthState]:
        """
        Find valid (non-expired) OAuth state for provider.

        Args:
            state: State parameter to search for
            provider: OAuth provider name

        Returns:
            OAuthState if found and valid, None otherwise
        """
        oauth_state_doc = self.collection.find_one({"state": state})
        if oauth_state_doc:
            oauth_state = OAuthState(**oauth_state_doc)
            if oauth_state.is_valid_for_provider(provider):
                return oauth_state
        return None
    
    def delete_by_state(self, state: str) -> bool:
        """
        Delete OAuth state by state parameter.
        
        Args:
            state: State parameter to delete
            
        Returns:
            True if deleted, False if not found
        """
        result = self.collection.delete_one({"state": state})
        return result.deleted_count > 0
    
    def cleanup_expired_states(self) -> int:
        """
        Clean up expired OAuth states.
        
        Returns:
            Number of expired states deleted
        """
        result = self.collection.delete_many({
            "expires_at": {"$lt": datetime.utcnow()}
        })
        return result.deleted_count
    
    def cleanup_states_for_user(self, user_id: str) -> int:
        """
        Clean up OAuth states for a specific user.
        
        Args:
            user_id: User ID to clean up states for
            
        Returns:
            Number of states deleted
        """
        result = self.collection.delete_many({"user_id": user_id})
        return result.deleted_count
    
    def get_states_for_provider(self, provider: str) -> list[OAuthState]:
        """
        Get all OAuth states for a specific provider.

        Args:
            provider: OAuth provider name

        Returns:
            List of OAuth states for the provider
        """
        oauth_states_docs = self.collection.find({"provider": provider})
        return [OAuthState(**doc) for doc in oauth_states_docs]
    
    def count_active_states(self) -> int:
        """
        Count active (non-expired) OAuth states.
        
        Returns:
            Number of active OAuth states
        """
        return self.collection.count_documents({
            "expires_at": {"$gt": datetime.utcnow()}
        })
    
    def get_expired_states(self) -> list[OAuthState]:
        """
        Get all expired OAuth states.

        Returns:
            List of expired OAuth states
        """
        expired_docs = self.collection.find({
            "expires_at": {"$lt": datetime.utcnow()}
        })
        return [OAuthState(**doc) for doc in expired_docs]
