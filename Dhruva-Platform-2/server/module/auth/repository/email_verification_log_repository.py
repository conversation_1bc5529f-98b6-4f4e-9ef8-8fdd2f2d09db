from datetime import datetime, timedelta
from typing import List, Optional

from fastapi import Depends
from pymongo.database import Database

from db.BaseRepository import BaseRepository
from db.database import AppDatabase

from ..model.email_verification_log import EmailVerificationLog


class EmailVerificationLogRepository(BaseRepository[EmailVerificationLog]):
    """
    Repository for managing email verification logs.
    
    This repository provides methods for storing and retrieving email verification
    logs for monitoring, debugging, and abuse prevention.
    """
    
    __collection_name__ = "email_verification_logs"

    def __init__(self, db: Database = Depends(AppDatabase)) -> None:
        super().__init__(db, self.__collection_name__)
    
    def find_by_email(self, email: str, limit: int = 100) -> List[EmailVerificationLog]:
        """Find verification logs for a specific email address."""
        results = self.collection.find({"email": email.lower()}).sort("timestamp", -1).limit(limit)
        return [EmailVerificationLog.parse_obj(log) for log in results]
    
    def find_by_ip_address(self, ip_address: str, hours: int = 24, limit: int = 100) -> List[EmailVerificationLog]:
        """Find verification logs for a specific IP address within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        results = self.collection.find({
            "ip_address": ip_address,
            "timestamp": {"$gte": cutoff_time}
        }).sort("timestamp", -1).limit(limit)
        return [EmailVerificationLog.parse_obj(log) for log in results]
    
    def find_by_action(self, action: str, hours: int = 24, limit: int = 100) -> List[EmailVerificationLog]:
        """Find verification logs for a specific action within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        results = self.collection.find({
            "action": action,
            "timestamp": {"$gte": cutoff_time}
        }).sort("timestamp", -1).limit(limit)
        return [EmailVerificationLog.parse_obj(log) for log in results]
    
    def count_signup_requests_by_ip(self, ip_address: str, hours: int = 1) -> int:
        """Count signup requests from a specific IP address within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return self.collection.count_documents({
            "ip_address": ip_address,
            "action": "signup_request",
            "timestamp": {"$gte": cutoff_time}
        })
    
    def count_verification_attempts_by_ip(self, ip_address: str, hours: int = 1) -> int:
        """Count verification attempts from a specific IP address within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return self.collection.count_documents({
            "ip_address": ip_address,
            "action": "verification_attempt",
            "timestamp": {"$gte": cutoff_time}
        })
    
    def count_resend_requests_by_email(self, email: str, hours: int = 1) -> int:
        """Count resend requests for a specific email within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return self.collection.count_documents({
            "email": email.lower(),
            "action": "resend_request",
            "timestamp": {"$gte": cutoff_time}
        })
    
    def delete_old_logs(self, days: int = 30) -> int:
        """Delete verification logs older than N days."""
        cutoff_time = datetime.utcnow() - timedelta(days=days)
        return self.delete_many({"timestamp": {"$lt": cutoff_time}})
    
    def get_verification_statistics(self, hours: int = 24) -> dict:
        """Get verification statistics for the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        pipeline = [
            {"$match": {"timestamp": {"$gte": cutoff_time}}},
            {"$group": {
                "_id": "$action",
                "total": {"$sum": 1},
                "successful": {"$sum": {"$cond": ["$success", 1, 0]}},
                "failed": {"$sum": {"$cond": ["$success", 0, 1]}}
            }}
        ]
        
        results = list(self.collection.aggregate(pipeline))
        
        # Convert to dictionary format
        stats = {}
        for result in results:
            action = result["_id"]
            stats[action] = {
                "total": result["total"],
                "successful": result["successful"],
                "failed": result["failed"],
                "success_rate": result["successful"] / result["total"] if result["total"] > 0 else 0
            }
        
        return stats
