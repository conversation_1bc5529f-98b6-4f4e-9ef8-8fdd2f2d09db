from datetime import datetime
from typing import List, Optional

from fastapi import Depends
from pymongo.database import Database

from db.BaseRepository import BaseRepository
from db.database import AppDatabase

from ..model.pending_registration import PendingRegistration


class PendingRegistrationRepository(BaseRepository[PendingRegistration]):
    """
    Repository for managing pending user registrations.
    
    This repository provides methods for storing, retrieving, and managing
    pending user registrations before email verification is completed.
    """
    
    __collection_name__ = "pending_registrations"

    def __init__(self, db: Database = Depends(AppDatabase)) -> None:
        super().__init__(db, self.__collection_name__)
    
    def find_by_email(self, email: str) -> Optional[PendingRegistration]:
        """Find a pending registration by email address."""
        return self.find_one({"email": email.lower()})
    
    def find_by_verification_token(self, token: str) -> Optional[PendingRegistration]:
        """Find a pending registration by verification token."""
        return self.find_one({"verification_token": token})
    
    def delete_by_email(self, email: str) -> int:
        """Delete a pending registration by email address."""
        return self.delete_many({"email": email.lower()})
    
    def delete_by_verification_token(self, token: str) -> int:
        """Delete a pending registration by verification token."""
        return self.delete_many({"verification_token": token})
    
    def find_expired_registrations(self) -> List[PendingRegistration]:
        """Find all expired pending registrations."""
        current_time = datetime.utcnow()
        return self.find({"expires_at": {"$lt": current_time}})
    
    def delete_expired_registrations(self) -> int:
        """Delete all expired pending registrations."""
        current_time = datetime.utcnow()
        return self.delete_many({"expires_at": {"$lt": current_time}})
    
    def count_pending_registrations(self) -> int:
        """Count total number of pending registrations."""
        return len(self.find_all())
    
    def find_registrations_by_ip(self, ip_address: str, hours: int = 24) -> List[PendingRegistration]:
        """Find pending registrations from a specific IP address within the last N hours."""
        cutoff_time = datetime.utcnow() - datetime.timedelta(hours=hours)
        return self.find({
            "ip_address": ip_address,
            "created_at": {"$gte": cutoff_time}
        })
    
    def update_verification_attempts(self, token: str) -> bool:
        """Increment verification attempts for a pending registration."""
        try:
            result = self.collection.update_one(
                {"verification_token": token},
                {"$inc": {"verification_attempts": 1}}
            )
            return result.modified_count > 0
        except Exception:
            return False
