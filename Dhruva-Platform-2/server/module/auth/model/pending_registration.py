from datetime import datetime
from typing import <PERSON>tional

from pydantic import EmailStr

from db.MongoBaseModel import MongoBaseModel


class PendingRegistration(MongoBaseModel):
    """
    Model for storing user registration data before email verification.
    
    This model stores user information temporarily until email verification
    is completed. After successful verification, the data is used to create
    a permanent user account and then deleted.
    """
    
    email: EmailStr
    name: str
    password_hash: Optional[str] = None  # None for OAuth users
    verification_token: str
    created_at: datetime
    expires_at: datetime
    verification_attempts: int = 0
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

    # OAuth-specific fields
    oauth_provider: Optional[str] = None  # "google", "github", etc.
    oauth_user_id: Optional[str] = None
    oauth_verified_email: Optional[bool] = None
    oauth_access_token: Optional[str] = None  # Encrypted
    oauth_refresh_token: Optional[str] = None  # Encrypted
    
    def is_oauth_registration(self) -> bool:
        """Check if this is an OAuth registration."""
        return self.oauth_provider is not None

    def is_traditional_registration(self) -> bool:
        """Check if this is a traditional email/password registration."""
        return self.password_hash is not None

    class Config:
        """Pydantic configuration for the model."""
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def increment_verification_attempts(self) -> None:
        """Increment the verification attempts counter."""
        self.verification_attempts += 1
    
    def is_expired(self) -> bool:
        """Check if the verification token has expired."""
        return datetime.utcnow() > self.expires_at
    
    def is_max_attempts_reached(self, max_attempts: int = 5) -> bool:
        """Check if maximum verification attempts have been reached."""
        return self.verification_attempts >= max_attempts
