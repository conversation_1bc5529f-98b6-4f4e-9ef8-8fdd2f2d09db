"""
OAuth state model for temporary storage of OAuth flow parameters.
Used for CSRF protection and PKCE implementation.
"""

from datetime import datetime, timedelta
from typing import Optional
from db.MongoBaseModel import MongoBaseModel


class OAuthState(MongoBaseModel):
    """
    Temporary storage for OAuth state and PKCE parameters.
    
    This model stores temporary data during OAuth flows to ensure security
    and maintain state between authorization request and callback.
    """
    
    state: str  # CSRF protection parameter
    code_verifier: str  # PKCE code verifier
    code_challenge: str  # PKCE code challenge
    provider: str  # OAuth provider name ("google", "github", etc.)
    redirect_uri: str  # Original redirect URI after successful auth
    user_id: Optional[str] = None  # For account linking flows
    created_at: datetime = datetime.utcnow()
    expires_at: datetime = datetime.utcnow() + timedelta(minutes=10)
    
    class Config:
        """Pydantic configuration for the model."""
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def is_expired(self) -> bool:
        """Check if the OAuth state has expired."""
        return datetime.utcnow() > self.expires_at
    
    def is_valid_for_provider(self, provider: str) -> bool:
        """Check if the state is valid for the specified provider."""
        return self.provider == provider and not self.is_expired()
    
    @classmethod
    def create_for_login(
        cls,
        state: str,
        code_verifier: str,
        code_challenge: str,
        provider: str,
        redirect_uri: str,
        expiry_minutes: int = 10
    ) -> "OAuthState":
        """
        Create OAuth state for login flow.
        
        Args:
            state: CSRF protection state parameter
            code_verifier: PKCE code verifier
            code_challenge: PKCE code challenge
            provider: OAuth provider name
            redirect_uri: Redirect URI after successful auth
            expiry_minutes: Expiration time in minutes
            
        Returns:
            OAuthState instance
        """
        return cls(
            state=state,
            code_verifier=code_verifier,
            code_challenge=code_challenge,
            provider=provider,
            redirect_uri=redirect_uri,
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(minutes=expiry_minutes)
        )
    
    @classmethod
    def create_for_linking(
        cls,
        state: str,
        code_verifier: str,
        code_challenge: str,
        provider: str,
        user_id: str,
        expiry_minutes: int = 10
    ) -> "OAuthState":
        """
        Create OAuth state for account linking flow.
        
        Args:
            state: CSRF protection state parameter
            code_verifier: PKCE code verifier
            code_challenge: PKCE code challenge
            provider: OAuth provider name
            user_id: ID of user linking the account
            expiry_minutes: Expiration time in minutes
            
        Returns:
            OAuthState instance
        """
        return cls(
            state=state,
            code_verifier=code_verifier,
            code_challenge=code_challenge,
            provider=provider,
            redirect_uri="/account/settings",  # Default redirect for linking
            user_id=user_id,
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(minutes=expiry_minutes)
        )
