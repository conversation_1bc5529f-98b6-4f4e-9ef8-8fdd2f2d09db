from datetime import datetime
from typing import <PERSON>tional

from pydantic import EmailStr

from db.MongoBaseModel import MongoBaseModel


class EmailVerificationLog(MongoBaseModel):
    """
    Model for tracking email verification attempts and preventing abuse.
    
    This model logs all verification-related activities including signup requests,
    verification attempts, and successful verifications. It helps with monitoring,
    debugging, and preventing abuse of the verification system.
    """
    
    email: EmailStr
    action: str  # "signup_request", "verification_attempt", "verification_success", "resend_request"
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    verification_token: Optional[str] = None  # For tracking specific tokens
    
    class Config:
        """Pydantic configuration for the model."""
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    @classmethod
    def create_signup_log(
        cls,
        email: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> "EmailVerificationLog":
        """Create a log entry for signup request."""
        return cls(
            email=email,
            action="signup_request",
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.utcnow(),
            success=success,
            error_message=error_message
        )
    
    @classmethod
    def create_verification_attempt_log(
        cls,
        email: str,
        verification_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = False,
        error_message: Optional[str] = None
    ) -> "EmailVerificationLog":
        """Create a log entry for verification attempt."""
        return cls(
            email=email,
            action="verification_attempt",
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.utcnow(),
            success=success,
            error_message=error_message,
            verification_token=verification_token
        )
    
    @classmethod
    def create_verification_success_log(
        cls,
        email: str,
        verification_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> "EmailVerificationLog":
        """Create a log entry for successful verification."""
        return cls(
            email=email,
            action="verification_success",
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.utcnow(),
            success=True,
            verification_token=verification_token
        )
    
    @classmethod
    def create_resend_request_log(
        cls,
        email: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> "EmailVerificationLog":
        """Create a log entry for resend verification request."""
        return cls(
            email=email,
            action="resend_request",
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.utcnow(),
            success=success,
            error_message=error_message
        )
