import os
import smtplib
import ssl
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from typing import Optional
import logging
from pathlib import Path

from jinja2 import Environment, FileSystemLoader


logger = logging.getLogger(__name__)


class EmailService:
    """
    Email service that supports multiple providers: SMTP, SendGrid, and Amazon SES.
    
    This service handles sending verification emails and welcome emails
    with proper template rendering and error handling.
    """
    
    def __init__(self):
        """Initialize email service with configuration from environment variables."""
        self.provider = os.environ.get("EMAIL_SERVICE_PROVIDER", "smtp").lower()
        self.from_address = os.environ.get("EMAIL_FROM_ADDRESS", "<EMAIL>")
        self.from_name = os.environ.get("EMAIL_FROM_NAME", "Dhruva Platform")
        self.frontend_base_url = os.environ.get("FRONTEND_BASE_URL", "http://localhost:3000")
        
        # Initialize template environment
        template_dir = Path(__file__).parent.parent / "templates"
        self.template_env = Environment(loader=FileSystemLoader(str(template_dir)))
        
        # Initialize provider-specific client
        self.client = self._initialize_client()
        
        logger.info(f"Email service initialized with provider: {self.provider}")
    
    def _initialize_client(self):
        """Initialize email client based on the configured provider."""
        if self.provider == "smtp":
            return self._initialize_smtp_client()
        elif self.provider == "sendgrid":
            return self._initialize_sendgrid_client()
        elif self.provider == "ses":
            return self._initialize_ses_client()
        else:
            raise ValueError(f"Unsupported email provider: {self.provider}")
    
    def _initialize_smtp_client(self):
        """Initialize SMTP client configuration."""
        return {
            "server": os.environ.get("SMTP_SERVER", "smtp.gmail.com"),
            "port": int(os.environ.get("SMTP_PORT", "587")),
            "username": os.environ.get("SMTP_USERNAME"),
            "password": os.environ.get("SMTP_PASSWORD"),
            "use_tls": os.environ.get("SMTP_USE_TLS", "true").lower() == "true"
        }
    
    def _initialize_sendgrid_client(self):
        """Initialize SendGrid client."""
        try:
            import sendgrid
            from sendgrid.helpers.mail import Mail
            
            api_key = os.environ.get("SENDGRID_API_KEY")
            if not api_key:
                raise ValueError("SENDGRID_API_KEY environment variable is required")
            
            return sendgrid.SendGridAPIClient(api_key=api_key)
        except ImportError:
            raise ImportError("SendGrid library not installed. Run: pip install sendgrid")
    
    def _initialize_ses_client(self):
        """Initialize Amazon SES client."""
        try:
            import boto3
            
            return boto3.client(
                'ses',
                aws_access_key_id=os.environ.get("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.environ.get("AWS_SECRET_ACCESS_KEY"),
                region_name=os.environ.get("AWS_REGION", "us-east-1")
            )
        except ImportError:
            raise ImportError("Boto3 library not installed. Run: pip install boto3")
    
    def _render_template(self, template_name: str, **kwargs) -> str:
        """Render email template with provided variables."""
        try:
            template = self.template_env.get_template(template_name)
            return template.render(**kwargs)
        except Exception as e:
            logger.error(f"Failed to render template {template_name}: {e}")
            raise
    
    async def send_verification_email(
        self,
        to_email: str,
        name: str,
        verification_token: str
    ) -> bool:
        """
        Send email verification email.
        
        Args:
            to_email: Recipient email address
            name: Recipient name
            verification_token: Verification token for the URL
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Generate verification URL
            verification_url = f"{self.frontend_base_url}/verify-email?token={verification_token}"
            
            # Render email content
            html_content = self._render_template(
                "verification_email.html",
                name=name,
                verification_url=verification_url,
                expires_in_hours=int(os.environ.get("EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS", "24"))
            )
            
            # Send email based on provider
            if self.provider == "smtp":
                return self._send_smtp_email(
                    to_email=to_email,
                    subject="Verify your email address for Dhruva Platform",
                    html_content=html_content
                )
            elif self.provider == "sendgrid":
                return await self._send_sendgrid_email(
                    to_email=to_email,
                    subject="Verify your email address for Dhruva Platform",
                    html_content=html_content
                )
            elif self.provider == "ses":
                return await self._send_ses_email(
                    to_email=to_email,
                    subject="Verify your email address for Dhruva Platform",
                    html_content=html_content
                )
            
        except Exception as e:
            logger.error(f"Failed to send verification email to {to_email}: {e}")
            return False
    
    async def send_welcome_email(
        self,
        to_email: str,
        name: str,
        api_key: str
    ) -> bool:
        """
        Send welcome email after successful verification.
        
        Args:
            to_email: Recipient email address
            name: Recipient name
            api_key: Generated API key (will be masked for security)
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Mask API key for security (show first 8 and last 4 characters)
            masked_api_key = api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:] if len(api_key) > 12 else api_key
            
            # Render email content
            html_content = self._render_template(
                "welcome_email.html",
                name=name,
                email=to_email,
                api_key=masked_api_key,
                frontend_base_url=self.frontend_base_url
            )
            
            # Send email based on provider
            if self.provider == "smtp":
                return self._send_smtp_email(
                    to_email=to_email,
                    subject="Welcome to Dhruva Platform - Your account is ready!",
                    html_content=html_content
                )
            elif self.provider == "sendgrid":
                return await self._send_sendgrid_email(
                    to_email=to_email,
                    subject="Welcome to Dhruva Platform - Your account is ready!",
                    html_content=html_content
                )
            elif self.provider == "ses":
                return await self._send_ses_email(
                    to_email=to_email,
                    subject="Welcome to Dhruva Platform - Your account is ready!",
                    html_content=html_content
                )
            
        except Exception as e:
            logger.error(f"Failed to send welcome email to {to_email}: {e}")
            return False
    
    def _send_smtp_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """Send email using SMTP."""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_address}>"
            message["To"] = to_email
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.client["server"], self.client["port"]) as server:
                if self.client["use_tls"]:
                    server.starttls(context=context)
                
                if self.client["username"] and self.client["password"]:
                    server.login(self.client["username"], self.client["password"])
                
                server.sendmail(self.from_address, to_email, message.as_string())
            
            logger.info(f"SMTP email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"SMTP email failed to {to_email}: {e}")
            return False

    async def _send_sendgrid_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """Send email using SendGrid."""
        try:
            from sendgrid.helpers.mail import Mail, From, To, Subject, HtmlContent

            # Create message
            from_email = From(self.from_address, self.from_name)
            to_email_obj = To(to_email)
            subject_obj = Subject(subject)
            html_content_obj = HtmlContent(html_content)

            mail = Mail(from_email, to_email_obj, subject_obj, html_content_obj)

            # Send email
            response = self.client.send(mail)

            if response.status_code in [200, 201, 202]:
                logger.info(f"SendGrid email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"SendGrid email failed to {to_email}: Status {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"SendGrid email failed to {to_email}: {e}")
            return False

    async def _send_ses_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """Send email using Amazon SES."""
        try:
            response = self.client.send_email(
                Source=f"{self.from_name} <{self.from_address}>",
                Destination={'ToAddresses': [to_email]},
                Message={
                    'Subject': {'Data': subject, 'Charset': 'UTF-8'},
                    'Body': {'Html': {'Data': html_content, 'Charset': 'UTF-8'}}
                }
            )

            if response['ResponseMetadata']['HTTPStatusCode'] == 200:
                logger.info(f"SES email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"SES email failed to {to_email}: {response}")
                return False

        except Exception as e:
            logger.error(f"SES email failed to {to_email}: {e}")
            return False

    def test_connection(self) -> bool:
        """Test email service connection and configuration."""
        try:
            if self.provider == "smtp":
                # Test SMTP connection
                context = ssl.create_default_context()
                with smtplib.SMTP(self.client["server"], self.client["port"]) as server:
                    if self.client["use_tls"]:
                        server.starttls(context=context)
                    if self.client["username"] and self.client["password"]:
                        server.login(self.client["username"], self.client["password"])
                logger.info("SMTP connection test successful")
                return True

            elif self.provider == "sendgrid":
                # Test SendGrid API key
                response = self.client.client.user.get()
                if response.status_code == 200:
                    logger.info("SendGrid connection test successful")
                    return True
                else:
                    logger.error(f"SendGrid connection test failed: {response.status_code}")
                    return False

            elif self.provider == "ses":
                # Test SES connection
                response = self.client.get_send_quota()
                if response['ResponseMetadata']['HTTPStatusCode'] == 200:
                    logger.info("SES connection test successful")
                    return True
                else:
                    logger.error(f"SES connection test failed: {response}")
                    return False

        except Exception as e:
            logger.error(f"Email service connection test failed: {e}")
            return False

    def get_service_info(self) -> dict:
        """Get information about the current email service configuration."""
        info = {
            "provider": self.provider,
            "from_address": self.from_address,
            "from_name": self.from_name,
            "frontend_base_url": self.frontend_base_url
        }

        if self.provider == "smtp":
            info.update({
                "smtp_server": self.client["server"],
                "smtp_port": self.client["port"],
                "smtp_use_tls": self.client["use_tls"]
            })

        return info
