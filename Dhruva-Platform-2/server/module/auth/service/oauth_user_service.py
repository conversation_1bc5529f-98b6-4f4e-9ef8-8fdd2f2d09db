"""
OAuth user service for managing OAuth users and account linking.
Handles user creation, authentication, and OAuth provider management.
"""

import os
import jwt
from datetime import datetime, timed<PERSON>ta
from typing import Tuple, Optional
from fastapi import HTTPException, status, Depends
from bson.objectid import ObjectId

from module.auth.model.user import User, OAuthProvider
from module.auth.repository.user_repository import UserRepository
from module.auth.service.auth_service import AuthService
from module.auth.service.email_verification_service import EmailVerificationService
from module.auth.service.google_oauth_service import GoogleUserInfo, GoogleOAuthTokens
from schema.auth.common import RoleType, ApiKeyType
from schema.auth.request import CreateA<PERSON><PERSON>eyRequest
from utils.encryption import encrypt_token, decrypt_token


class OAuthUserService:
    """Service for managing OAuth users and account linking."""

    def __init__(
        self,
        user_repository: UserRepository = Depends(),
        auth_service: AuthService = Depends(),
        email_verification_service: EmailVerificationService = Depends()
    ):
        self.user_repository = user_repository
        self.auth_service = auth_service
        self.email_verification_service = email_verification_service
    
    async def handle_oauth_user(
        self,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> Tuple[User, str]:
        """
        Handle OAuth user login/registration.
        
        Args:
            provider: OAuth provider name ("google")
            user_info: User information from OAuth provider
            tokens: OAuth tokens from provider
            
        Returns:
            Tuple[User, str]: (user_object, jwt_token)
        """
        # Check if user exists by email
        existing_user = self.user_repository.find_one({"email": user_info.email})
        
        if existing_user:
            # Update existing user with OAuth info
            user = await self._update_existing_user_oauth(
                existing_user, provider, user_info, tokens
            )
        else:
            # Create new OAuth user
            user = await self._create_new_oauth_user(
                provider, user_info, tokens
            )
        
        # Generate JWT token for session
        jwt_token = self._generate_oauth_jwt_token(user, provider)
        
        # Update last login
        user.last_login = datetime.utcnow()
        self.user_repository.save(user)
        
        return user, jwt_token
    
    async def _create_new_oauth_user(
        self,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Create new user from OAuth information."""
        
        # Check if email verification is needed
        needs_verification = not user_info.verified_email
        
        if needs_verification:
            # Create pending registration for unverified OAuth email
            await self._create_oauth_pending_registration(user_info, tokens, provider)
            raise HTTPException(
                status_code=status.HTTP_202_ACCEPTED,
                detail={
                    "message": "Email verification required",
                    "email": user_info.email,
                    "verification_required": True,
                    "provider": provider
                }
            )
        
        # Create OAuth provider info
        oauth_provider = OAuthProvider(
            provider=provider,
            provider_user_id=user_info.id,
            email=user_info.email,
            verified_email=user_info.verified_email,
            access_token=encrypt_token(tokens.access_token),
            refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
            token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
            scope=tokens.scope,
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )
        
        # Create new user
        new_user = User(
            name=user_info.name,
            email=user_info.email,
            password=None,  # OAuth-only user
            role=RoleType.CONSUMER,
            oauth_providers=[oauth_provider],
            created_via=provider,
            email_verified=user_info.verified_email,
            created_at=datetime.utcnow()
        )
        
        # Save user
        user_id = self.user_repository.insert_one(new_user)
        created_user = self.user_repository.get_by_id(ObjectId(str(user_id)))
        
        # Generate default API key
        try:
            api_request = CreateApiKeyRequest(
                name="default",
                type=ApiKeyType.INFERENCE,
                regenerate=False,
                target_user_id=str(created_user.id),
                data_tracking=False,
            )
            
            self.auth_service.create_api_key(
                request=api_request,
                id=ObjectId(str(created_user.id)),
            )
        except Exception as e:
            print(f"Warning: Failed to create default API key for OAuth user: {e}")
        
        return created_user
    
    async def _update_existing_user_oauth(
        self,
        existing_user: User,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Update existing user with OAuth information."""
        
        # Check if user already has this OAuth provider
        existing_oauth = existing_user.get_oauth_provider(provider)
        
        if existing_oauth:
            # Update existing OAuth provider info
            existing_oauth.access_token = encrypt_token(tokens.access_token)
            existing_oauth.refresh_token = encrypt_token(tokens.refresh_token) if tokens.refresh_token else None
            existing_oauth.token_expires_at = datetime.utcnow() + timedelta(seconds=tokens.expires_in)
            existing_oauth.scope = tokens.scope
            existing_oauth.last_login = datetime.utcnow()
            existing_oauth.verified_email = user_info.verified_email
        else:
            # Add new OAuth provider
            oauth_provider = OAuthProvider(
                provider=provider,
                provider_user_id=user_info.id,
                email=user_info.email,
                verified_email=user_info.verified_email,
                access_token=encrypt_token(tokens.access_token),
                refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
                token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
                scope=tokens.scope,
                created_at=datetime.utcnow(),
                last_login=datetime.utcnow()
            )
            existing_user.oauth_providers.append(oauth_provider)
        
        # Update email verification status if OAuth email is verified
        if user_info.verified_email and not existing_user.email_verified:
            existing_user.email_verified = True
        
        # Save updated user
        self.user_repository.save(existing_user)
        
        return existing_user
    
    def _generate_oauth_jwt_token(self, user: User, provider: str) -> str:
        """Generate JWT token for OAuth user session."""
        
        payload = {
            "sub": str(user.id),
            "name": user.name,
            "email": user.email,
            "role": user.role,
            "auth_type": "oauth",
            "oauth_provider": provider,
            "exp": datetime.utcnow() + timedelta(days=30),
            "iat": datetime.utcnow(),
        }
        
        # Get JWT secret key
        jwt_secret = os.environ.get("JWT_SECRET_KEY")
        if not jwt_secret:
            raise ValueError("JWT_SECRET_KEY not configured")
        
        token = jwt.encode(
            payload,
            jwt_secret,
            algorithm="HS256",
            headers={"tok": "oauth"}
        )
        
        return token
    
    async def link_oauth_account(
        self,
        user_id: ObjectId,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Link OAuth account to existing user."""
        
        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user already has this provider
        if user.has_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{provider.title()} account already linked"
            )
        
        # Check if OAuth email matches user email
        if user.email != user_info.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OAuth email does not match account email"
            )
        
        # Add OAuth provider
        oauth_provider = OAuthProvider(
            provider=provider,
            provider_user_id=user_info.id,
            email=user_info.email,
            verified_email=user_info.verified_email,
            access_token=encrypt_token(tokens.access_token),
            refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
            token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
            scope=tokens.scope,
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )
        
        user.oauth_providers.append(oauth_provider)
        
        # Update email verification if OAuth email is verified
        if user_info.verified_email and not user.email_verified:
            user.email_verified = True
        
        self.user_repository.save(user)
        return user
    
    async def unlink_oauth_account(self, user_id: ObjectId, provider: str) -> User:
        """Unlink OAuth account from user."""
        
        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user has this provider
        if not user.has_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{provider.title()} account not linked"
            )
        
        # Check if user can safely unlink this provider
        if not user.can_unlink_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot unlink last authentication method. Please set a password first."
            )
        
        # Remove OAuth provider
        user.oauth_providers = [
            oauth for oauth in user.oauth_providers 
            if oauth.provider != provider
        ]
        
        self.user_repository.save(user)
        return user
    
    async def _create_oauth_pending_registration(
        self,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens,
        provider: str
    ):
        """Create pending registration for OAuth user with unverified email."""
        
        # This would integrate with the existing email verification service
        # For now, we'll raise an exception to indicate email verification is needed
        # In a full implementation, this would create a pending registration
        # and send a verification email
        
        print(f"OAuth user {user_info.email} needs email verification")
        print(f"Provider: {provider}, Verified: {user_info.verified_email}")
        
        # TODO: Implement OAuth pending registration creation
        # This should integrate with the existing EmailVerificationService
