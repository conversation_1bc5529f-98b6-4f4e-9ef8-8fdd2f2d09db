import os
import secrets
import traceback
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from argon2 import PasswordHasher
from bson import ObjectId
from fastapi import Depends, status
from fastapi.exceptions import HTTPException

from exception.base_error import BaseError
from exception.client_error import ClientError
from module.auth.error.errors import Errors
from schema.auth.common import RoleType, ApiKeyType
from schema.auth.request.create_api_key_request import CreateApiKeyRequest
from schema.auth.request.email_verification_request import (
    EmailVerificationRequest,
    RegistrationStatusRequest,
    ResendVerificationRequest,
)
from schema.auth.response.email_verification_response import (
    EmailVerificationResponse,
    RegistrationStatusResponse,
    ResendVerificationResponse,
)

from ..model.email_verification_log import EmailVerificationLog
from ..model.pending_registration import PendingRegistration
from ..model.user import User
from ..repository.email_verification_log_repository import EmailVerificationLogRepository
from ..repository.pending_registration_repository import PendingRegistrationRepository
from ..repository.user_repository import UserRepository
from .auth_service import AuthService
from .email_service import EmailService


logger = logging.getLogger(__name__)


class EmailVerificationService:
    """
    Service for handling email verification workflow.
    
    This service manages the complete email verification process including
    creating pending registrations, sending verification emails, validating
    tokens, and creating user accounts after successful verification.
    """
    
    def __init__(
        self,
        pending_registration_repository: PendingRegistrationRepository = Depends(PendingRegistrationRepository),
        email_verification_log_repository: EmailVerificationLogRepository = Depends(EmailVerificationLogRepository),
        user_repository: UserRepository = Depends(UserRepository),
        auth_service: AuthService = Depends(AuthService),
        email_service: EmailService = Depends(EmailService)
    ):
        self.pending_registration_repository = pending_registration_repository
        self.email_verification_log_repository = email_verification_log_repository
        self.user_repository = user_repository
        self.auth_service = auth_service
        self.email_service = email_service
    
    def generate_verification_token(self) -> str:
        """Generate a cryptographically secure verification token."""
        # Generate 48 random bytes (will be 64 chars when base64 encoded)
        return secrets.token_urlsafe(48)
    
    def generate_token_expiration(self, hours: int = 24) -> datetime:
        """Generate token expiration timestamp."""
        return datetime.utcnow() + timedelta(hours=hours)
    
    async def create_pending_registration(
        self,
        name: str,
        email: str,
        password: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> str:
        """
        Create a pending registration and send verification email.
        
        Args:
            name: User's full name
            email: User's email address
            password: User's password (will be hashed)
            ip_address: Optional IP address for logging
            user_agent: Optional user agent for logging
            
        Returns:
            str: Verification token
            
        Raises:
            ClientError: If email already exists or registration fails
        """
        try:
            # Check if user already exists
            existing_user = self.user_repository.find_one({"email": email})
            if existing_user:
                # Log the attempt
                log_entry = EmailVerificationLog.create_signup_log(
                    email=email,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Email already registered"
                )
                self.email_verification_log_repository.insert_one(log_entry)
                
                raise ClientError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message="Email already registered or verification pending",
                )
            
            # Check if there's already a pending registration for this email
            existing_pending = self.pending_registration_repository.find_by_email(email)
            if existing_pending:
                # Delete the existing pending registration to allow new one
                self.pending_registration_repository.delete_by_email(email)
            
            # Hash password
            ph = PasswordHasher()
            hashed_password = ph.hash(password)
            
            # Generate verification token and expiration
            verification_token = self.generate_verification_token()
            expires_at = self.generate_token_expiration()
            
            # Create pending registration
            pending_registration = PendingRegistration(
                email=email,
                name=name,
                password_hash=hashed_password,
                verification_token=verification_token,
                created_at=datetime.utcnow(),
                expires_at=expires_at,
                verification_attempts=0,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Save pending registration
            self.pending_registration_repository.insert_one(pending_registration)
            
            # Send verification email
            email_sent = await self.email_service.send_verification_email(
                to_email=email,
                name=name,
                verification_token=verification_token
            )

            # Check if we're in development/test mode (allow graceful degradation)
            test_mode = os.environ.get("EMAIL_TEST_MODE", "false").lower() == "true"

            if not email_sent and not test_mode:
                # Clean up pending registration if email failed
                self.pending_registration_repository.delete_by_verification_token(verification_token)

                # Log the failure
                log_entry = EmailVerificationLog.create_signup_log(
                    email=email,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Failed to send verification email"
                )
                self.email_verification_log_repository.insert_one(log_entry)

                raise ClientError(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    message="Unable to send verification email. Please try again.",
                )
            elif not email_sent and test_mode:
                # In test mode, log the failure but continue
                logger.warning(f"Email sending failed in test mode for {email}, continuing anyway")
                log_entry = EmailVerificationLog.create_signup_log(
                    email=email,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Email sending failed in test mode (continuing)"
                )
                self.email_verification_log_repository.insert_one(log_entry)
            
            # Log successful signup request
            log_entry = EmailVerificationLog.create_signup_log(
                email=email,
                ip_address=ip_address,
                user_agent=user_agent,
                success=True
            )
            self.email_verification_log_repository.insert_one(log_entry)
            
            return verification_token
            
        except ClientError:
            raise
        except Exception:
            raise BaseError(Errors.DHRUVA201.value, traceback.format_exc())
    
    async def verify_email(
        self,
        token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> EmailVerificationResponse:
        """
        Verify email using verification token and create user account.
        
        Args:
            token: Verification token
            ip_address: Optional IP address for logging
            user_agent: Optional user agent for logging
            
        Returns:
            EmailVerificationResponse: User details and API key
            
        Raises:
            ClientError: If token is invalid, expired, or verification fails
        """
        try:
            # Find pending registration by token
            pending_registration = self.pending_registration_repository.find_by_verification_token(token)
            
            if not pending_registration:
                # Log failed verification attempt with a valid email format
                log_entry = EmailVerificationLog.create_verification_attempt_log(
                    email="<EMAIL>",
                    verification_token=token,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Invalid verification token"
                )
                self.email_verification_log_repository.insert_one(log_entry)
                
                raise ClientError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message="Invalid or expired verification token",
                )
            
            # Check if token has expired
            if pending_registration.is_expired():
                # Log expired token attempt
                log_entry = EmailVerificationLog.create_verification_attempt_log(
                    email=pending_registration.email,
                    verification_token=token,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Verification token expired"
                )
                self.email_verification_log_repository.insert_one(log_entry)
                
                # Clean up expired registration
                self.pending_registration_repository.delete_by_verification_token(token)
                
                raise ClientError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message="Verification token has expired. Please request a new one.",
                )
            
            # Check if maximum attempts reached
            max_attempts = int(os.environ.get("EMAIL_VERIFICATION_MAX_ATTEMPTS", "5"))
            if pending_registration.is_max_attempts_reached(max_attempts):
                # Log max attempts reached
                log_entry = EmailVerificationLog.create_verification_attempt_log(
                    email=pending_registration.email,
                    verification_token=token,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Maximum verification attempts reached"
                )
                self.email_verification_log_repository.insert_one(log_entry)
                
                # Clean up registration
                self.pending_registration_repository.delete_by_verification_token(token)
                
                raise ClientError(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    message="Too many verification attempts. Please request a new verification email.",
                )
            
            # Increment verification attempts
            self.pending_registration_repository.update_verification_attempts(token)
            
            # Check if email is already verified (user exists)
            existing_user = self.user_repository.find_one({"email": pending_registration.email})
            if existing_user:
                # Log already verified attempt
                log_entry = EmailVerificationLog.create_verification_attempt_log(
                    email=pending_registration.email,
                    verification_token=token,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Email already verified"
                )
                self.email_verification_log_repository.insert_one(log_entry)
                
                # Clean up pending registration
                self.pending_registration_repository.delete_by_verification_token(token)
                
                raise ClientError(
                    status_code=status.HTTP_409_CONFLICT,
                    message="Email already verified and account exists",
                )
            
            # Create user account using existing auth service logic
            new_user = User(
                name=pending_registration.name,
                email=pending_registration.email,
                password=pending_registration.password_hash,  # Already hashed
                role=RoleType.CONSUMER,
            )
            
            user_id = self.user_repository.insert_one(new_user)
            created_user = self.user_repository.get_by_id(ObjectId(str(user_id)))
            
            # Generate default API key
            api_request = CreateApiKeyRequest(
                name="default",
                type=ApiKeyType.INFERENCE,
                regenerate=False,
                target_user_id=str(created_user.id),
                data_tracking=False,
            )
            
            api_key = self.auth_service.create_api_key(
                request=api_request,
                id=ObjectId(str(created_user.id)),
            )
            
            # Send welcome email (optional, don't fail if it doesn't work)
            try:
                await self.email_service.send_welcome_email(
                    to_email=created_user.email,
                    name=created_user.name,
                    api_key=api_key
                )
            except Exception as e:
                # Log warning but don't fail the verification
                logger.warning(f"Failed to send welcome email to {created_user.email}: {e}")
            
            # Log successful verification
            log_entry = EmailVerificationLog.create_verification_success_log(
                email=pending_registration.email,
                verification_token=token,
                ip_address=ip_address,
                user_agent=user_agent
            )
            self.email_verification_log_repository.insert_one(log_entry)
            
            # Clean up pending registration
            self.pending_registration_repository.delete_by_verification_token(token)
            
            return EmailVerificationResponse(
                message="Email verified successfully. Account created.",
                user_id=str(created_user.id),
                email=created_user.email,
                role=created_user.role,
                api_key=api_key,
                redirect_url=f"{os.environ.get('FRONTEND_BASE_URL', 'http://localhost:3000')}/welcome"
            )
            
        except ClientError:
            raise
        except Exception:
            raise BaseError(Errors.DHRUVA207.value, traceback.format_exc())

    async def resend_verification_email(
        self,
        email: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> ResendVerificationResponse:
        """
        Resend verification email for a pending registration.

        Args:
            email: Email address to resend verification to
            ip_address: Optional IP address for logging
            user_agent: Optional user agent for logging

        Returns:
            ResendVerificationResponse: Confirmation of email resent

        Raises:
            ClientError: If email not found or resend fails
        """
        try:
            # Check if user already exists
            existing_user = self.user_repository.find_one({"email": email})
            if existing_user:
                # Log the attempt
                log_entry = EmailVerificationLog.create_resend_request_log(
                    email=email,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Email already verified"
                )
                self.email_verification_log_repository.insert_one(log_entry)

                raise ClientError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message="Email already verified. Please sign in.",
                )

            # Find pending registration
            pending_registration = self.pending_registration_repository.find_by_email(email)
            if not pending_registration:
                # Log the attempt
                log_entry = EmailVerificationLog.create_resend_request_log(
                    email=email,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="No pending registration found"
                )
                self.email_verification_log_repository.insert_one(log_entry)

                raise ClientError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message="No pending registration found for this email",
                )

            # Generate new token and expiration
            new_token = self.generate_verification_token()
            new_expires_at = self.generate_token_expiration()

            # Update pending registration with new token
            pending_registration.verification_token = new_token
            pending_registration.expires_at = new_expires_at
            pending_registration.verification_attempts = 0  # Reset attempts

            self.pending_registration_repository.save(pending_registration)

            # Send verification email
            email_sent = await self.email_service.send_verification_email(
                to_email=email,
                name=pending_registration.name,
                verification_token=new_token
            )

            if not email_sent:
                # Log the failure
                log_entry = EmailVerificationLog.create_resend_request_log(
                    email=email,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    error_message="Failed to send verification email"
                )
                self.email_verification_log_repository.insert_one(log_entry)

                raise ClientError(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    message="Unable to resend verification email. Please try again.",
                )

            # Log successful resend
            log_entry = EmailVerificationLog.create_resend_request_log(
                email=email,
                ip_address=ip_address,
                user_agent=user_agent,
                success=True
            )
            self.email_verification_log_repository.insert_one(log_entry)

            return ResendVerificationResponse(
                message="Verification email resent successfully",
                email=email,
                expires_in=86400  # 24 hours
            )

        except ClientError:
            raise
        except Exception:
            raise BaseError(Errors.DHRUVA201.value, traceback.format_exc())

    def get_registration_status(self, email: str) -> RegistrationStatusResponse:
        """
        Get the registration status for an email address.

        Args:
            email: Email address to check

        Returns:
            RegistrationStatusResponse: Status information
        """
        try:
            # Check if user already exists (verified)
            existing_user = self.user_repository.find_one({"email": email})
            if existing_user:
                return RegistrationStatusResponse(
                    status="verified",
                    message="Email is already verified and account exists",
                    email=email
                )

            # Check for pending registration
            pending_registration = self.pending_registration_repository.find_by_email(email)
            if pending_registration:
                if pending_registration.is_expired():
                    # Clean up expired registration
                    self.pending_registration_repository.delete_by_email(email)
                    return RegistrationStatusResponse(
                        status="not_found",
                        message="No pending registration found",
                        email=email
                    )

                return RegistrationStatusResponse(
                    status="pending",
                    message="Email verification is pending",
                    email=email,
                    expires_at=pending_registration.expires_at,
                    verification_attempts=pending_registration.verification_attempts
                )

            # No registration found
            return RegistrationStatusResponse(
                status="not_found",
                message="No registration found for this email",
                email=email
            )

        except Exception:
            raise BaseError(Errors.DHRUVA206.value, traceback.format_exc())
