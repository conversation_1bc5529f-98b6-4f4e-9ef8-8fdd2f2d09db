"""
Token encryption utility for OAuth tokens.
Provides secure encryption/decryption of OAuth access and refresh tokens.
"""

import os
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class TokenEncryption:
    """Utility for encrypting/decrypting OAuth tokens using AES-256."""
    
    def __init__(self):
        # Get encryption key from environment
        key = os.environ.get("OAUTH_TOKEN_ENCRYPTION_KEY")
        if not key:
            # Generate a default key for development (NOT for production)
            key = "dhruva-oauth-encryption-key-change-in-production"
            print("⚠️  WARNING: Using default encryption key. Set OAUTH_TOKEN_ENCRYPTION_KEY in production!")
        
        # Derive Fernet key from the provided key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'dhruva_oauth_salt',  # Use consistent salt
            iterations=100000,
        )
        key_bytes = base64.urlsafe_b64encode(kdf.derive(key.encode()))
        self.fernet = Fernet(key_bytes)
    
    def encrypt(self, token: str) -> str:
        """
        Encrypt a token.
        
        Args:
            token: Plain text token to encrypt
            
        Returns:
            Base64 encoded encrypted token
        """
        if not token:
            return ""
        
        try:
            encrypted_bytes = self.fernet.encrypt(token.encode())
            return base64.urlsafe_b64encode(encrypted_bytes).decode()
        except Exception as e:
            raise ValueError(f"Failed to encrypt token: {str(e)}")
    
    def decrypt(self, encrypted_token: str) -> str:
        """
        Decrypt a token.
        
        Args:
            encrypted_token: Base64 encoded encrypted token
            
        Returns:
            Plain text token
        """
        if not encrypted_token:
            return ""
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_token.encode())
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception as e:
            raise ValueError(f"Failed to decrypt token: {str(e)}")


# Global instance
_token_encryption = None


def get_token_encryption() -> TokenEncryption:
    """Get global token encryption instance."""
    global _token_encryption
    if _token_encryption is None:
        _token_encryption = TokenEncryption()
    return _token_encryption


def encrypt_token(token: str) -> str:
    """Encrypt a token using global encryption instance."""
    return get_token_encryption().encrypt(token)


def decrypt_token(encrypted_token: str) -> str:
    """Decrypt a token using global encryption instance."""
    return get_token_encryption().decrypt(encrypted_token)


# Test function for development
def test_encryption():
    """Test encryption/decryption functionality."""
    test_token = "test_access_token_12345"
    
    try:
        # Test encryption
        encrypted = encrypt_token(test_token)
        print(f"Original: {test_token}")
        print(f"Encrypted: {encrypted}")
        
        # Test decryption
        decrypted = decrypt_token(encrypted)
        print(f"Decrypted: {decrypted}")
        
        # Verify
        assert test_token == decrypted, "Encryption/decryption failed!"
        print("✅ Encryption test passed!")
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")


if __name__ == "__main__":
    test_encryption()
