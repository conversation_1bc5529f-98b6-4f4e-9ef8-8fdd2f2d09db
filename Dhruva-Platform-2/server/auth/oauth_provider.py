"""
OAuth authentication provider for validating OAuth tokens.
Integrates with the existing authentication system to support OAuth tokens.
"""

import os
import jwt
from datetime import datetime
from typing import Optional
from fastapi import Request
from pymongo.database import Database
from bson.objectid import ObjectId

from module.auth.model.user import User
from module.auth.service.google_oauth_service import GoogleOAuthService


class OAuthAuthenticationProvider:
    """OAuth authentication provider for validating OAuth tokens."""
    
    def __init__(self):
        self.google_service = GoogleOAuthService()
    
    def validate_credentials(self, credentials: str, request: Request, db: Database) -> bool:
        """
        Validate OAuth credentials (JWT token with OAuth context).
        
        Args:
            credentials: JWT token containing OAuth user information
            request: FastAPI request object
            db: Database connection
            
        Returns:
            True if credentials are valid, False otherwise
        """
        try:
            # Get JWT secret key
            jwt_secret = os.environ.get("JWT_SECRET_KEY")
            if not jwt_secret:
                print("JWT_SECRET_KEY not configured")
                return False
            
            # Decode JWT token (should contain OAuth user info)
            claims = jwt.decode(
                credentials, 
                key=jwt_secret, 
                algorithms=["HS256"]
            )
            
            # Verify this is an OAuth token
            if claims.get("auth_type") != "oauth":
                return False
            
            # Verify token is not expired
            exp = claims.get("exp")
            if exp and datetime.utcnow().timestamp() > exp:
                return False
            
            # Get user from database
            user_collection = db["user"]
            user_doc = user_collection.find_one({"_id": ObjectId(claims["sub"])})
            
            if not user_doc:
                return False
            
            # Verify user has OAuth provider
            oauth_provider = claims.get("oauth_provider")
            if oauth_provider:
                user = User(**user_doc)
                if not user.has_oauth_provider(oauth_provider):
                    return False
            
            # Populate request state
            request.state.user_id = claims["sub"]
            request.state.auth_type = "oauth"
            request.state.oauth_provider = oauth_provider
            request.state.user_name = claims.get("name", "")
            request.state.user_email = claims.get("email", "")
            request.state.user_role = claims.get("role", "CONSUMER")
            
            # For inference/feedback endpoints, get default API key
            if self._is_inference_endpoint(request.url.path):
                api_key_collection = db["api_key"]
                api_key = api_key_collection.find_one(
                    {
                        "name": "default", 
                        "user_id": ObjectId(claims["sub"]),
                        "active": True
                    }
                )
                
                if api_key:
                    request.state.api_key_id = api_key["_id"]
                    request.state.api_key_type = api_key["type"]
                    request.state.api_key_name = "default"
                    request.state.api_key_data_tracking = api_key.get("data_tracking", False)
            
            return True
            
        except jwt.ExpiredSignatureError:
            print("OAuth JWT token expired")
            return False
        except jwt.InvalidTokenError:
            print("Invalid OAuth JWT token")
            return False
        except Exception as e:
            print(f"OAuth token validation error: {e}")
            return False
    
    def fetch_session(self, credentials: str, db: Database) -> dict:
        """
        Fetch session information for OAuth user.
        
        Args:
            credentials: JWT token containing OAuth user information
            db: Database connection
            
        Returns:
            User session information
            
        Raises:
            Exception: If session cannot be fetched
        """
        try:
            # Get JWT secret key
            jwt_secret = os.environ.get("JWT_SECRET_KEY")
            if not jwt_secret:
                raise Exception("JWT_SECRET_KEY not configured")
            
            # Decode JWT token
            claims = jwt.decode(
                credentials, 
                key=jwt_secret, 
                algorithms=["HS256"]
            )
            
            # Verify this is an OAuth token
            if claims.get("auth_type") != "oauth":
                raise Exception("Not an OAuth token")
            
            # Get user from database
            user_collection = db["user"]
            user_doc = user_collection.find_one({"_id": ObjectId(claims["sub"])})
            
            if not user_doc:
                raise Exception("User not found")
            
            return {
                "_id": user_doc["_id"],
                "name": user_doc["name"],
                "email": user_doc["email"],
                "role": user_doc["role"],
                "auth_type": "oauth",
                "oauth_provider": claims.get("oauth_provider"),
                "email_verified": user_doc.get("email_verified", False),
                "created_via": user_doc.get("created_via", "email")
            }
            
        except jwt.ExpiredSignatureError:
            raise Exception("OAuth JWT token expired")
        except jwt.InvalidTokenError:
            raise Exception("Invalid OAuth JWT token")
        except Exception as e:
            raise Exception(f"Failed to fetch OAuth session: {str(e)}")
    
    def _is_inference_endpoint(self, path: str) -> bool:
        """
        Check if the request path is an inference endpoint.
        
        Args:
            path: Request URL path
            
        Returns:
            True if it's an inference endpoint, False otherwise
        """
        inference_paths = [
            "/services/inference/",
            "/services/feedback/",
            "/services/details/"
        ]
        
        return any(inference_path in path for inference_path in inference_paths)
    
    def refresh_oauth_token(self, user_id: str, provider: str, db: Database) -> Optional[str]:
        """
        Refresh OAuth token for user.
        
        Args:
            user_id: User ID
            provider: OAuth provider name
            db: Database connection
            
        Returns:
            New access token if successful, None otherwise
        """
        try:
            # Get user from database
            user_collection = db["user"]
            user_doc = user_collection.find_one({"_id": ObjectId(user_id)})
            
            if not user_doc:
                return None
            
            user = User(**user_doc)
            oauth_provider = user.get_oauth_provider(provider)
            
            if not oauth_provider or not oauth_provider.refresh_token:
                return None
            
            # Refresh token based on provider
            if provider == "google":
                from utils.encryption import decrypt_token
                refresh_token = decrypt_token(oauth_provider.refresh_token)
                
                # Use Google service to refresh token
                new_tokens = self.google_service.refresh_access_token(refresh_token)
                
                # Update user's OAuth provider with new tokens
                from utils.encryption import encrypt_token
                oauth_provider.access_token = encrypt_token(new_tokens.access_token)
                oauth_provider.token_expires_at = datetime.utcnow() + timedelta(seconds=new_tokens.expires_in)
                
                # Save updated user
                user_collection.replace_one({"_id": ObjectId(user_id)}, user.dict())
                
                return new_tokens.access_token
            
            return None
            
        except Exception as e:
            print(f"Failed to refresh OAuth token: {e}")
            return None
    
    def validate_oauth_provider_token(self, access_token: str, provider: str) -> bool:
        """
        Validate OAuth provider token directly with the provider.
        
        Args:
            access_token: OAuth access token
            provider: OAuth provider name
            
        Returns:
            True if token is valid, False otherwise
        """
        try:
            if provider == "google":
                # Validate with Google's tokeninfo endpoint
                import httpx
                response = httpx.get(
                    f"https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={access_token}",
                    timeout=10.0
                )
                return response.status_code == 200
            
            return False
            
        except Exception:
            return False


# Global instance
oauth_provider = OAuthAuthenticationProvider()
