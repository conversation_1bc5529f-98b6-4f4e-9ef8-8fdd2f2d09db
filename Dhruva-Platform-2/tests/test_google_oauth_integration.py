"""
Test suite for Google OAuth2 integration.
Tests the complete OAuth flow, security measures, and user management.
"""

import pytest
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from bson.objectid import ObjectId

# Import the main app
from main import app

# Import OAuth-related modules
from module.auth.service.google_oauth_service import GoogleOAuthService, GoogleUserInfo, GoogleOAuthTokens
from module.auth.service.oauth_user_service import OAuthUserService
from module.auth.model.oauth_state import OAuthState
from module.auth.model.user import User, OAuthProvider
from utils.encryption import encrypt_token, decrypt_token


class TestGoogleOAuthFlow:
    """Test Google OAuth2 integration end-to-end."""
    
    def setup_method(self):
        """Set up test environment."""
        self.client = TestClient(app)
        
        # Mock environment variables
        os.environ["GOOGLE_OAUTH_CLIENT_ID"] = "test-client-id.apps.googleusercontent.com"
        os.environ["GOOGLE_OAUTH_CLIENT_SECRET"] = "test-client-secret"
        os.environ["GOOGLE_OAUTH_REDIRECT_URI"] = "http://localhost:8000/auth/oauth/google/callback"
        os.environ["JWT_SECRET_KEY"] = "test-jwt-secret-key"
        os.environ["OAUTH_TOKEN_ENCRYPTION_KEY"] = "test-encryption-key-32-chars-long"
        os.environ["FRONTEND_BASE_URL"] = "http://localhost:3000"
        os.environ["OAUTH_SUCCESS_REDIRECT_URL"] = "http://localhost:3000/oauth/success"
        os.environ["OAUTH_ERROR_REDIRECT_URL"] = "http://localhost:3000/oauth/error"
    
    def test_oauth_login_initiation(self):
        """Test OAuth login URL generation."""
        response = self.client.get("/auth/oauth/google/login")
        
        assert response.status_code == 302
        location = response.headers.get("location", "")
        assert "accounts.google.com" in location
        assert "code_challenge" in location
        assert "state" in location
        assert "client_id=test-client-id.apps.googleusercontent.com" in location
    
    def test_oauth_login_with_custom_redirect(self):
        """Test OAuth login with custom redirect URI."""
        custom_redirect = "http://localhost:3000/dashboard"
        response = self.client.get(f"/auth/oauth/google/login?redirect_uri={custom_redirect}")
        
        assert response.status_code == 302
        # The state should be stored in the database with the custom redirect
    
    @patch('module.auth.service.google_oauth_service.httpx.AsyncClient')
    def test_oauth_callback_with_valid_code(self, mock_client):
        """Test OAuth callback with valid authorization code."""
        # Mock Google token exchange response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test-access-token",
            "refresh_token": "test-refresh-token",
            "expires_in": 3600,
            "token_type": "Bearer",
            "scope": "openid email profile"
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Mock Google user info response
        mock_userinfo_response = Mock()
        mock_userinfo_response.status_code = 200
        mock_userinfo_response.json.return_value = {
            "id": "123456789",
            "email": "<EMAIL>",
            "verified_email": True,
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User",
            "picture": "https://example.com/photo.jpg"
        }
        mock_client_instance.get.return_value = mock_userinfo_response
        
        # Create a valid OAuth state first
        # This would require mocking the database operations
        
        response = self.client.get(
            "/auth/oauth/google/callback?code=test-code&state=test-state"
        )
        
        # Should redirect to success page
        assert response.status_code == 302
    
    def test_oauth_callback_with_invalid_state(self):
        """Test OAuth callback with invalid state parameter."""
        response = self.client.get(
            "/auth/oauth/google/callback?code=test-code&state=invalid-state"
        )
        
        # Should redirect to error page
        assert response.status_code == 302
        location = response.headers.get("location", "")
        assert "oauth/error" in location or "error=" in location
    
    def test_oauth_callback_with_error(self):
        """Test OAuth callback with error from Google."""
        response = self.client.get(
            "/auth/oauth/google/callback?error=access_denied&state=test-state"
        )
        
        # Should redirect to error page
        assert response.status_code == 302
        location = response.headers.get("location", "")
        assert "error=access_denied" in location


class TestGoogleOAuthService:
    """Test Google OAuth service functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        os.environ["GOOGLE_OAUTH_CLIENT_ID"] = "test-client-id.apps.googleusercontent.com"
        os.environ["GOOGLE_OAUTH_CLIENT_SECRET"] = "test-client-secret"
        os.environ["GOOGLE_OAUTH_REDIRECT_URI"] = "http://localhost:8000/auth/oauth/google/callback"
        
        self.service = GoogleOAuthService()
    
    def test_pkce_generation(self):
        """Test PKCE code verifier and challenge generation."""
        code_verifier, code_challenge = self.service.generate_pkce_pair()
        
        assert len(code_verifier) >= 43
        assert len(code_challenge) >= 43
        assert code_verifier != code_challenge
        
        # Verify code challenge is base64url encoded SHA256 of verifier
        import base64
        import hashlib
        expected_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        assert code_challenge == expected_challenge
    
    def test_authorization_url_generation(self):
        """Test OAuth authorization URL generation."""
        state = "test-state"
        code_challenge = "test-challenge"
        
        auth_url = self.service.generate_authorization_url(state, code_challenge)
        
        assert "accounts.google.com/o/oauth2/v2/auth" in auth_url
        assert f"client_id={self.service.config.CLIENT_ID}" in auth_url
        assert f"state={state}" in auth_url
        assert f"code_challenge={code_challenge}" in auth_url
        assert "code_challenge_method=S256" in auth_url
        assert "scope=openid%20email%20profile" in auth_url
    
    @patch('module.auth.service.google_oauth_service.httpx.AsyncClient')
    async def test_token_exchange(self, mock_client):
        """Test authorization code to token exchange."""
        # Mock successful token response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test-access-token",
            "refresh_token": "test-refresh-token",
            "expires_in": 3600,
            "token_type": "Bearer",
            "scope": "openid email profile"
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        tokens = await self.service.exchange_code_for_tokens(
            code="test-code",
            code_verifier="test-verifier"
        )
        
        assert tokens.access_token == "test-access-token"
        assert tokens.refresh_token == "test-refresh-token"
        assert tokens.expires_in == 3600
        assert tokens.token_type == "Bearer"
    
    @patch('module.auth.service.google_oauth_service.httpx.AsyncClient')
    async def test_user_info_retrieval(self, mock_client):
        """Test user information retrieval from Google."""
        # Mock user info response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "123456789",
            "email": "<EMAIL>",
            "verified_email": True,
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User",
            "picture": "https://example.com/photo.jpg"
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        user_info = await self.service.get_user_info("test-access-token")
        
        assert user_info.id == "123456789"
        assert user_info.email == "<EMAIL>"
        assert user_info.verified_email is True
        assert user_info.name == "Test User"


class TestOAuthSecurity:
    """Test OAuth security measures."""
    
    def test_token_encryption(self):
        """Test OAuth token encryption and decryption."""
        os.environ["OAUTH_TOKEN_ENCRYPTION_KEY"] = "test-encryption-key-32-chars-long"
        
        original_token = "test-access-token-12345"
        
        # Test encryption
        encrypted_token = encrypt_token(original_token)
        assert encrypted_token != original_token
        assert len(encrypted_token) > len(original_token)
        
        # Test decryption
        decrypted_token = decrypt_token(encrypted_token)
        assert decrypted_token == original_token
    
    def test_oauth_state_model(self):
        """Test OAuth state model functionality."""
        state = OAuthState.create_for_login(
            state="test-state",
            code_verifier="test-verifier",
            code_challenge="test-challenge",
            provider="google",
            redirect_uri="/dashboard"
        )
        
        assert state.state == "test-state"
        assert state.provider == "google"
        assert not state.is_expired()
        assert state.is_valid_for_provider("google")
        assert not state.is_valid_for_provider("github")
    
    def test_oauth_state_expiration(self):
        """Test OAuth state expiration."""
        # Create expired state
        expired_state = OAuthState(
            state="test-state",
            code_verifier="test-verifier",
            code_challenge="test-challenge",
            provider="google",
            redirect_uri="/dashboard",
            created_at=datetime.utcnow() - timedelta(minutes=20),
            expires_at=datetime.utcnow() - timedelta(minutes=10)
        )
        
        assert expired_state.is_expired()
        assert not expired_state.is_valid_for_provider("google")


class TestOAuthUserManagement:
    """Test OAuth user creation and management."""
    
    def test_user_model_oauth_methods(self):
        """Test User model OAuth-related methods."""
        oauth_provider = OAuthProvider(
            provider="google",
            provider_user_id="123456789",
            email="<EMAIL>",
            verified_email=True,
            access_token="encrypted-access-token",
            refresh_token="encrypted-refresh-token",
            token_expires_at=datetime.utcnow() + timedelta(hours=1),
            scope="openid email profile",
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )
        
        user = User(
            name="Test User",
            email="<EMAIL>",
            password=None,  # OAuth-only user
            role="CONSUMER",
            oauth_providers=[oauth_provider],
            created_via="google",
            email_verified=True
        )
        
        assert user.has_oauth_provider("google")
        assert not user.has_oauth_provider("github")
        assert user.is_oauth_only()
        assert user.get_oauth_provider("google") == oauth_provider
        assert not user.can_unlink_oauth_provider("google")  # Only provider
    
    def test_user_with_multiple_auth_methods(self):
        """Test user with both password and OAuth."""
        oauth_provider = OAuthProvider(
            provider="google",
            provider_user_id="123456789",
            email="<EMAIL>",
            verified_email=True,
            access_token="encrypted-access-token",
            scope="openid email profile",
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )
        
        user = User(
            name="Test User",
            email="<EMAIL>",
            password="hashed-password",  # Has password
            role="CONSUMER",
            oauth_providers=[oauth_provider],
            created_via="email",
            email_verified=True
        )
        
        assert user.has_oauth_provider("google")
        assert not user.is_oauth_only()
        assert user.can_unlink_oauth_provider("google")  # Has password backup


class TestBackwardCompatibility:
    """Test backward compatibility with existing authentication."""
    
    def test_existing_jwt_authentication(self):
        """Ensure existing JWT auth still works."""
        # This would test that existing JWT tokens still work
        # and that the AuthProvider correctly routes to auth_token_provider
        pass
    
    def test_existing_api_key_authentication(self):
        """Ensure existing API key auth still works."""
        # This would test that existing API keys still work
        # and that the AuthProvider correctly routes to api_key_provider
        pass
    
    def test_mixed_authentication_scenarios(self):
        """Test scenarios with multiple auth methods."""
        # Test user with both password and OAuth
        # Test API key generation for OAuth users
        # Test session management across auth types
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
