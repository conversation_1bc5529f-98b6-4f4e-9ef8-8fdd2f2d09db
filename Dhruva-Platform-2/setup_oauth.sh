#!/bin/bash

# Dhruva Platform OAuth2 Setup Script
echo "🚀 Setting up Google OAuth2 for Dhruva Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "server/main.py" ]; then
    print_error "Please run this script from the Dhruva-Platform-2 root directory"
    exit 1
fi

print_info "Setting up backend OAuth environment..."

# 1. Copy OAuth environment file
if [ ! -f ".env" ]; then
    print_warning "No .env file found. Creating from .env.oauth..."
    cp .env.oauth .env
else
    print_info "Appending OAuth configuration to existing .env file..."
    echo "" >> .env
    echo "# OAuth Configuration" >> .env
    cat .env.oauth >> .env
fi

print_status "OAuth environment variables configured"

# 2. Update Docker image with OAuth dependencies
print_info "OAuth dependencies added to requirements.txt"
print_info "Building Docker image with OAuth support..."

# Check if Docker is available
if command -v docker &> /dev/null; then
    print_info "Rebuilding Docker image with OAuth dependencies..."
    cd server
    docker build -t dhruva-platform-server:latest-pg15 .
    if [ $? -eq 0 ]; then
        print_status "Docker image rebuilt successfully with OAuth support"
    else
        print_error "Failed to build Docker image"
        exit 1
    fi
    cd ..
else
    print_warning "Docker not found. Please rebuild the Docker image manually:"
    echo "cd server && docker build -t dhruva-platform-server:latest-pg15 ."
fi

# 3. Setup frontend
print_info "Setting up frontend OAuth integration..."
cd client

# Install any missing dependencies (if needed)
if [ -f "package.json" ]; then
    print_info "Frontend dependencies should already be installed"
else
    print_error "package.json not found in client directory"
fi

cd ..

print_status "Frontend OAuth integration configured"

# 4. Display setup summary
echo ""
echo "🎉 OAuth2 Setup Complete!"
echo ""
echo "📋 Setup Summary:"
echo "✅ Google OAuth2 credentials configured"
echo "✅ Backend OAuth endpoints added"
echo "✅ Frontend OAuth integration added"
echo "✅ Environment variables configured"
echo ""
echo "🔧 Your OAuth2 Configuration:"
echo "Client ID: 235810295758-lhf7ekmbta2hjp0dd580lvm0301o1lue.apps.googleusercontent.com"
echo "Redirect URI: http://localhost:8000/auth/oauth/google/callback"
echo "Frontend URL: http://localhost:3001"
echo ""
echo "🚀 Next Steps:"
echo "1. Deploy with Docker (recommended):"
echo "   docker compose -f docker-compose-db.yml -f docker-compose-metering.yml -f docker-compose-monitoring.yml -f docker-compose-app.yml up --force-recreate -d --remove-orphans"
echo ""
echo "2. OR start the backend server directly:"
echo "   cd server && python main.py"
echo ""
echo "3. Start the frontend (in another terminal):"
echo "   cd client && npm run dev"
echo ""
echo "3. Test OAuth login:"
echo "   - Go to http://localhost:3001/dhruva"
echo "   - Click 'Continue with Google' button"
echo "   - Complete Google OAuth flow"
echo ""
echo "🔍 Troubleshooting:"
echo "- Check server logs: docker logs dhruva-platform-server --tail 100"
echo "- Verify environment variables are loaded"
echo "- Ensure Google Cloud Console redirect URIs match"
echo ""
echo "📚 Documentation:"
echo "- Setup Guide: GOOGLE_OAUTH2_CREDENTIALS_SETUP.md"
echo "- Deployment Guide: GOOGLE_OAUTH2_DEPLOYMENT_GUIDE.md"
echo "- Implementation Summary: OAUTH2_IMPLEMENTATION_SUMMARY.md"
echo ""
print_status "Ready to test Google OAuth2 integration!"
